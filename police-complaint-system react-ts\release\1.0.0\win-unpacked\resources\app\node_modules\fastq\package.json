{"name": "fastq", "version": "1.19.1", "description": "Fast, in memory work queue", "main": "queue.js", "pre-commit": ["test", "typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/fastq.git"}, "author": "<PERSON> <<EMAIL>>", "license": "ISC", "homepage": "https://github.com/mcollina/fastq#readme", "devDependencies": {"async": "^3.1.0", "neo-async": "^2.6.1", "nyc": "^17.0.0", "pre-commit": "^1.2.2", "snazzy": "^9.0.0", "standard": "^16.0.0", "tape": "^5.0.0", "typescript": "^5.0.4"}, "dependencies": {"reusify": "^1.0.4"}, "standard": {"ignore": ["example.mjs"]}}