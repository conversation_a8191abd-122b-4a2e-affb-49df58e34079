import React, { useState } from 'react'
import {
  X,
  User,
  Phone,
  MapPin,
  Calendar,
  FileText,
  AlertCircle,
  Clock,
  Search,
  CheckCircle,
  XCircle,
  Printer,
  Loader
} from 'lucide-react'
import { pdfGenerator } from '../services/pdfGenerator'

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

interface ComplaintDetailModalProps {
  complaint: Complaint | null
  isOpen: boolean
  onClose: () => void
}

const ComplaintDetailModal: React.FC<ComplaintDetailModalProps> = ({
  complaint,
  isOpen,
  onClose
}) => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  if (!isOpen || !complaint) return null

  const handlePrintReport = async () => {
    try {
      setIsGeneratingPDF(true)
      await pdfGenerator.generateComplaintReport(complaint)
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Erreur lors de la génération du rapport PDF')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const getStatusInfo = (status: string) => {
    const statusConfig = {
      pending: { 
        label: 'En Attente', 
        labelAr: 'في الانتظار', 
        class: 'bg-yellow-100 text-yellow-800', 
        icon: Clock 
      },
      investigating: { 
        label: 'En Investigation', 
        labelAr: 'قيد التحقيق', 
        class: 'bg-blue-100 text-blue-800', 
        icon: Search 
      },
      resolved: { 
        label: 'Résolue', 
        labelAr: 'محلولة', 
        class: 'bg-green-100 text-green-800', 
        icon: CheckCircle 
      },
      closed: { 
        label: 'Fermée', 
        labelAr: 'مغلقة', 
        class: 'bg-gray-100 text-gray-800', 
        icon: XCircle 
      }
    }
    
    return statusConfig[status as keyof typeof statusConfig]
  }

  const getPriorityInfo = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Faible', labelAr: 'منخفض', class: 'bg-green-100 text-green-800' },
      medium: { label: 'Moyen', labelAr: 'متوسط', class: 'bg-yellow-100 text-yellow-800' },
      high: { label: 'Élevé', labelAr: 'عالي', class: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'Urgent', labelAr: 'عاجل', class: 'bg-red-100 text-red-800' }
    }
    
    return priorityConfig[priority as keyof typeof priorityConfig]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const statusInfo = getStatusInfo(complaint.status)
  const priorityInfo = getPriorityInfo(complaint.priority)
  const StatusIcon = statusInfo?.icon

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Détails de la Plainte
            </h2>
            <p className="text-sm text-gray-600 mt-1">تفاصيل الشكوى</p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="btn-secondary text-sm flex items-center"
              onClick={handlePrintReport}
              disabled={isGeneratingPDF}
            >
              {isGeneratingPDF ? (
                <Loader className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Printer className="w-4 h-4 mr-2" />
              )}
              {isGeneratingPDF ? 'Génération...' : 'Imprimer PDF'}
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Informations Générales / معلومات عامة
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <FileText className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Numéro de Plainte</p>
                    <p className="text-lg font-bold text-blue-600">{complaint.complaint_number}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Date d'Enregistrement</p>
                    <p className="text-sm text-gray-900">{formatDate(complaint.date_registered)}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div
                    className="w-5 h-5 rounded-full mr-3"
                    style={{ backgroundColor: complaint.color_code }}
                  ></div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Type de Plainte</p>
                    <p className="text-sm text-gray-900">{complaint.type_name_fr}</p>
                    <p className="text-sm text-gray-600 text-arabic">{complaint.type_name_ar}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  {StatusIcon && <StatusIcon className="w-5 h-5 text-gray-400 mr-3" />}
                  <div>
                    <p className="text-sm font-medium text-gray-700">Statut</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo?.class}`}>
                      {statusInfo?.label} / {statusInfo?.labelAr}
                    </span>
                  </div>
                </div>

                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Priorité</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityInfo?.class}`}>
                      {priorityInfo?.label} / {priorityInfo?.labelAr}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Informations du Plaignant / معلومات المشتكي
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Nom Complet</p>
                    <p className="text-sm text-gray-900">{complaint.complainant_name}</p>
                  </div>
                </div>

                {complaint.complainant_id_number && (
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Numéro d'Identité</p>
                      <p className="text-sm text-gray-900">{complaint.complainant_id_number}</p>
                    </div>
                  </div>
                )}

                {complaint.complainant_phone && (
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Téléphone</p>
                      <p className="text-sm text-gray-900">{complaint.complainant_phone}</p>
                    </div>
                  </div>
                )}

                {complaint.complainant_address && (
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Adresse</p>
                      <p className="text-sm text-gray-900">{complaint.complainant_address}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Incident Details */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Détails de l'Incident / تفاصيل الحادث
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
              <div className="flex items-center">
                <MapPin className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Lieu de l'Incident</p>
                  <p className="text-sm text-gray-900">{complaint.location_incident}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Date de l'Incident</p>
                  <p className="text-sm text-gray-900">
                    {new Date(complaint.date_incident).toLocaleDateString('fr-FR')}
                  </p>
                </div>
              </div>
            </div>

            {/* Descriptions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {complaint.description_fr && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2">Description en Français</p>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">
                      {complaint.description_fr}
                    </p>
                  </div>
                </div>
              )}

              {complaint.description_ar && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-2 text-arabic">الوصف بالعربية</p>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap text-arabic" dir="rtl">
                      {complaint.description_ar}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {complaint.evidence_notes && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Notes sur les Preuves / ملاحظات الأدلة
                </h3>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">
                    {complaint.evidence_notes}
                  </p>
                </div>
              </div>
            )}

            {complaint.officer_notes && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Notes de l'Officier / ملاحظات الضابط
                </h3>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">
                    {complaint.officer_notes}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Officer Information */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Informations Officielles / معلومات رسمية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center">
                <User className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Officier Responsable</p>
                  <p className="text-sm text-gray-900">{complaint.officer_in_charge}</p>
                </div>
              </div>

              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Créée le</p>
                  <p className="text-sm text-gray-900">
                    {complaint.created_at ? formatDate(complaint.created_at) : 'N/A'}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Clock className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-700">Dernière Modification</p>
                  <p className="text-sm text-gray-900">
                    {complaint.updated_at ? formatDate(complaint.updated_at) : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            Fermer
          </button>
          <button
            className="btn-primary"
            onClick={() => {/* TODO: Implement edit */}}
          >
            Modifier
          </button>
        </div>
      </div>
    </div>
  )
}

export default ComplaintDetailModal
