{"version": 3, "file": "electron-locator.js", "sourceRoot": "", "sources": ["../../test/electron-locator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+BAA8B;AAC9B,6CAA+B;AAC/B,2CAA6B;AAE7B,8DAA+D;AAE/D,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAA;AAE7E,KAAK,UAAU,wBAAwB,CAAC,eAAuB,EAAE,QAAgB;IAC/E,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QAC1C,MAAM,YAAY,GAAG,MAAM,IAAA,uCAAoB,EAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC3E,IAAA,aAAM,EAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvC,oEAAoE;QACpE,IAAA,aAAM,EAAC,MAAM,EAAE,CAAC,UAAU,CAAC,YAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;AACL,CAAC;AAED,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;QAE9E,MAAM,CAAC,KAAK,IAAI,EAAE;YAChB,MAAM,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,GAAG,WAAW,QAAQ,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YAC9D,IAAA,aAAM,EAAC,MAAM,IAAA,uCAAoB,EAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,KAAK,IAAI,EAAE;YACf,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,QAAQ,EAAE,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0DAA0D,EAAE,GAAG,EAAE;QACxE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAC9D,wBAAwB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QACjE,wBAAwB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QACvD,wBAAwB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAEjD,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAC1D,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}