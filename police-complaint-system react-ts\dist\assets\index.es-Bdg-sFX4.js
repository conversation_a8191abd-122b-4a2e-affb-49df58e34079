import{c as Qe,_ as za,g as Io}from"./index-3e1DpzIh.js";var ht=function(a){return a&&a.Math===Math&&a},_=ht(typeof globalThis=="object"&&globalThis)||ht(typeof window=="object"&&window)||ht(typeof self=="object"&&self)||ht(typeof Qe=="object"&&Qe)||ht(typeof Qe=="object"&&Qe)||function(){return this}()||Function("return this")(),Et={},D=function(a){try{return!!a()}catch{return!0}},$l=D,he=!$l(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7}),wl=D,br=!wl(function(){var a=(function(){}).bind();return typeof a!="function"||a.hasOwnProperty("prototype")}),Cl=br,Ft=Function.prototype.call,Y=Cl?Ft.bind(Ft):function(){return Ft.apply(Ft,arguments)},Mo={},_o={}.propertyIsEnumerable,Vo=Object.getOwnPropertyDescriptor,Al=Vo&&!_o.call({1:2},1);Mo.f=Al?function(e){var t=Vo(this,e);return!!t&&t.enumerable}:_o;var xi=function(a,e){return{enumerable:!(a&1),configurable:!(a&2),writable:!(a&4),value:e}},Do=br,Lo=Function.prototype,Ha=Lo.call,Pl=Do&&Lo.bind.bind(Ha,Ha),L=Do?Pl:function(a){return function(){return Ha.apply(a,arguments)}},ko=L,Rl=ko({}.toString),Nl=ko("".slice),je=function(a){return Nl(Rl(a),8,-1)},Il=L,Ml=D,_l=je,Zr=Object,Vl=Il("".split),Bo=Ml(function(){return!Zr("z").propertyIsEnumerable(0)})?function(a){return _l(a)==="String"?Vl(a,""):Zr(a)}:Zr,xr=function(a){return a==null},Dl=xr,Ll=TypeError,ve=function(a){if(Dl(a))throw new Ll("Can't call method on "+a);return a},kl=Bo,Bl=ve,$t=function(a){return kl(Bl(a))},Jr=typeof document=="object"&&document.all,B=typeof Jr>"u"&&Jr!==void 0?function(a){return typeof a=="function"||a===Jr}:function(a){return typeof a=="function"},jl=B,ae=function(a){return typeof a=="object"?a!==null:jl(a)},ea=_,Fl=B,Ul=function(a){return Fl(a)?a:void 0},Fe=function(a,e){return arguments.length<2?Ul(ea[a]):ea[a]&&ea[a][e]},Gl=L,Tr=Gl({}.isPrototypeOf),zl=_,gn=zl.navigator,dn=gn&&gn.userAgent,wt=dn?String(dn):"",jo=_,ta=wt,pn=jo.process,yn=jo.Deno,mn=pn&&pn.versions||yn&&yn.version,bn=mn&&mn.v8,le,cr;bn&&(le=bn.split("."),cr=le[0]>0&&le[0]<4?1:+(le[0]+le[1]));!cr&&ta&&(le=ta.match(/Edge\/(\d+)/),(!le||le[1]>=74)&&(le=ta.match(/Chrome\/(\d+)/),le&&(cr=+le[1])));var Ti=cr,xn=Ti,Hl=D,Yl=_,Xl=Yl.String,Fo=!!Object.getOwnPropertySymbols&&!Hl(function(){var a=Symbol("symbol detection");return!Xl(a)||!(Object(a)instanceof Symbol)||!Symbol.sham&&xn&&xn<41}),Wl=Fo,Uo=Wl&&!Symbol.sham&&typeof Symbol.iterator=="symbol",ql=Fe,Ql=B,Kl=Tr,Zl=Uo,Jl=Object,Go=Zl?function(a){return typeof a=="symbol"}:function(a){var e=ql("Symbol");return Ql(e)&&Kl(e.prototype,Jl(a))},eh=String,Or=function(a){try{return eh(a)}catch{return"Object"}},th=B,rh=Or,ah=TypeError,Te=function(a){if(th(a))return a;throw new ah(rh(a)+" is not a function")},ih=Te,nh=xr,st=function(a,e){var t=a[e];return nh(t)?void 0:ih(t)},ra=Y,aa=B,ia=ae,sh=TypeError,oh=function(a,e){var t,r;if(e==="string"&&aa(t=a.toString)&&!ia(r=ra(t,a))||aa(t=a.valueOf)&&!ia(r=ra(t,a))||e!=="string"&&aa(t=a.toString)&&!ia(r=ra(t,a)))return r;throw new sh("Can't convert object to primitive value")},zo={exports:{}},Tn=_,uh=Object.defineProperty,Oi=function(a,e){try{uh(Tn,a,{value:e,configurable:!0,writable:!0})}catch{Tn[a]=e}return e},lh=_,hh=Oi,On="__core-js_shared__",Sn=zo.exports=lh[On]||hh(On,{});(Sn.versions||(Sn.versions=[])).push({version:"3.43.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.43.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Si=zo.exports,En=Si,Ei=function(a,e){return En[a]||(En[a]=e||{})},vh=ve,fh=Object,Sr=function(a){return fh(vh(a))},ch=L,gh=Sr,dh=ch({}.hasOwnProperty),fe=Object.hasOwn||function(e,t){return dh(gh(e),t)},ph=L,yh=0,mh=Math.random(),bh=ph(1.1.toString),Ho=function(a){return"Symbol("+(a===void 0?"":a)+")_"+bh(++yh+mh,36)},xh=_,Th=Ei,$n=fe,Oh=Ho,Sh=Fo,Eh=Uo,Ke=xh.Symbol,na=Th("wks"),$h=Eh?Ke.for||Ke:Ke&&Ke.withoutSetter||Oh,z=function(a){return $n(na,a)||(na[a]=Sh&&$n(Ke,a)?Ke[a]:$h("Symbol."+a)),na[a]},wh=Y,wn=ae,Cn=Go,Ch=st,Ah=oh,Ph=z,Rh=TypeError,Nh=Ph("toPrimitive"),Ih=function(a,e){if(!wn(a)||Cn(a))return a;var t=Ch(a,Nh),r;if(t){if(e===void 0&&(e="default"),r=wh(t,a,e),!wn(r)||Cn(r))return r;throw new Rh("Can't convert object to primitive value")}return e===void 0&&(e="number"),Ah(a,e)},Mh=Ih,_h=Go,Yo=function(a){var e=Mh(a,"string");return _h(e)?e:e+""},Vh=_,An=ae,Ya=Vh.document,Dh=An(Ya)&&An(Ya.createElement),Er=function(a){return Dh?Ya.createElement(a):{}},Lh=he,kh=D,Bh=Er,Xo=!Lh&&!kh(function(){return Object.defineProperty(Bh("div"),"a",{get:function(){return 7}}).a!==7}),jh=he,Fh=Y,Uh=Mo,Gh=xi,zh=$t,Hh=Yo,Yh=fe,Xh=Xo,Pn=Object.getOwnPropertyDescriptor;Et.f=jh?Pn:function(e,t){if(e=zh(e),t=Hh(t),Xh)try{return Pn(e,t)}catch{}if(Yh(e,t))return Gh(!Fh(Uh.f,e,t),e[t])};var Oe={},Wh=he,qh=D,Wo=Wh&&qh(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42}),Qh=ae,Kh=String,Zh=TypeError,J=function(a){if(Qh(a))return a;throw new Zh(Kh(a)+" is not an object")},Jh=he,ev=Xo,tv=Wo,Ut=J,Rn=Yo,rv=TypeError,sa=Object.defineProperty,av=Object.getOwnPropertyDescriptor,oa="enumerable",ua="configurable",la="writable";Oe.f=Jh?tv?function(e,t,r){if(Ut(e),t=Rn(t),Ut(r),typeof e=="function"&&t==="prototype"&&"value"in r&&la in r&&!r[la]){var i=av(e,t);i&&i[la]&&(e[t]=r.value,r={configurable:ua in r?r[ua]:i[ua],enumerable:oa in r?r[oa]:i[oa],writable:!1})}return sa(e,t,r)}:sa:function(e,t,r){if(Ut(e),t=Rn(t),Ut(r),ev)try{return sa(e,t,r)}catch{}if("get"in r||"set"in r)throw new rv("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var iv=he,nv=Oe,sv=xi,Ct=iv?function(a,e,t){return nv.f(a,e,sv(1,t))}:function(a,e,t){return a[e]=t,a},qo={exports:{}},Xa=he,ov=fe,Qo=Function.prototype,uv=Xa&&Object.getOwnPropertyDescriptor,Ko=ov(Qo,"name"),lv=Ko&&(function(){}).name==="something",hv=Ko&&(!Xa||Xa&&uv(Qo,"name").configurable),$r={PROPER:lv,CONFIGURABLE:hv},vv=L,fv=B,Wa=Si,cv=vv(Function.toString);fv(Wa.inspectSource)||(Wa.inspectSource=function(a){return cv(a)});var $i=Wa.inspectSource,gv=_,dv=B,Nn=gv.WeakMap,pv=dv(Nn)&&/native code/.test(String(Nn)),yv=Ei,mv=Ho,In=yv("keys"),wi=function(a){return In[a]||(In[a]=mv(a))},Ci={},bv=pv,Zo=_,xv=ae,Tv=Ct,ha=fe,va=Si,Ov=wi,Sv=Ci,Mn="Object already initialized",qa=Zo.TypeError,Ev=Zo.WeakMap,gr,Tt,dr,$v=function(a){return dr(a)?Tt(a):gr(a,{})},wv=function(a){return function(e){var t;if(!xv(e)||(t=Tt(e)).type!==a)throw new qa("Incompatible receiver, "+a+" required");return t}};if(bv||va.state){var de=va.state||(va.state=new Ev);de.get=de.get,de.has=de.has,de.set=de.set,gr=function(a,e){if(de.has(a))throw new qa(Mn);return e.facade=a,de.set(a,e),e},Tt=function(a){return de.get(a)||{}},dr=function(a){return de.has(a)}}else{var Ye=Ov("state");Sv[Ye]=!0,gr=function(a,e){if(ha(a,Ye))throw new qa(Mn);return e.facade=a,Tv(a,Ye,e),e},Tt=function(a){return ha(a,Ye)?a[Ye]:{}},dr=function(a){return ha(a,Ye)}}var wr={set:gr,get:Tt,has:dr,enforce:$v,getterFor:wv},Ai=L,Cv=D,Av=B,Gt=fe,Qa=he,Pv=$r.CONFIGURABLE,Rv=$i,Jo=wr,Nv=Jo.enforce,Iv=Jo.get,_n=String,or=Object.defineProperty,Mv=Ai("".slice),_v=Ai("".replace),Vv=Ai([].join),Dv=Qa&&!Cv(function(){return or(function(){},"length",{value:8}).length!==8}),Lv=String(String).split("String"),kv=qo.exports=function(a,e,t){Mv(_n(e),0,7)==="Symbol("&&(e="["+_v(_n(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),t&&t.getter&&(e="get "+e),t&&t.setter&&(e="set "+e),(!Gt(a,"name")||Pv&&a.name!==e)&&(Qa?or(a,"name",{value:e,configurable:!0}):a.name=e),Dv&&t&&Gt(t,"arity")&&a.length!==t.arity&&or(a,"length",{value:t.arity});try{t&&Gt(t,"constructor")&&t.constructor?Qa&&or(a,"prototype",{writable:!1}):a.prototype&&(a.prototype=void 0)}catch{}var r=Nv(a);return Gt(r,"source")||(r.source=Vv(Lv,typeof e=="string"?e:"")),a};Function.prototype.toString=kv(function(){return Av(this)&&Iv(this).source||Rv(this)},"toString");var eu=qo.exports,Bv=B,jv=Oe,Fv=eu,Uv=Oi,Ue=function(a,e,t,r){r||(r={});var i=r.enumerable,n=r.name!==void 0?r.name:e;if(Bv(t)&&Fv(t,n,r),r.global)i?a[e]=t:Uv(e,t);else{try{r.unsafe?a[e]&&(i=!0):delete a[e]}catch{}i?a[e]=t:jv.f(a,e,{value:t,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return a},tu={},Gv=Math.ceil,zv=Math.floor,Hv=Math.trunc||function(e){var t=+e;return(t>0?zv:Gv)(t)},Yv=Hv,Cr=function(a){var e=+a;return e!==e||e===0?0:Yv(e)},Xv=Cr,Wv=Math.max,qv=Math.min,Qv=function(a,e){var t=Xv(a);return t<0?Wv(t+e,0):qv(t,e)},Kv=Cr,Zv=Math.min,ot=function(a){var e=Kv(a);return e>0?Zv(e,9007199254740991):0},Jv=ot,Pi=function(a){return Jv(a.length)},ef=$t,tf=Qv,rf=Pi,af=function(a){return function(e,t,r){var i=ef(e),n=rf(i);if(n===0)return!a&&-1;var o=tf(r,n),s;if(a&&t!==t){for(;n>o;)if(s=i[o++],s!==s)return!0}else for(;n>o;o++)if((a||o in i)&&i[o]===t)return a||o||0;return!a&&-1}},ru={indexOf:af(!1)},nf=L,fa=fe,sf=$t,of=ru.indexOf,uf=Ci,Vn=nf([].push),au=function(a,e){var t=sf(a),r=0,i=[],n;for(n in t)!fa(uf,n)&&fa(t,n)&&Vn(i,n);for(;e.length>r;)fa(t,n=e[r++])&&(~of(i,n)||Vn(i,n));return i},Ri=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],lf=au,hf=Ri,vf=hf.concat("length","prototype");tu.f=Object.getOwnPropertyNames||function(e){return lf(e,vf)};var iu={};iu.f=Object.getOwnPropertySymbols;var ff=Fe,cf=L,gf=tu,df=iu,pf=J,yf=cf([].concat),mf=ff("Reflect","ownKeys")||function(e){var t=gf.f(pf(e)),r=df.f;return r?yf(t,r(e)):t},Dn=fe,bf=mf,xf=Et,Tf=Oe,Of=function(a,e,t){for(var r=bf(e),i=Tf.f,n=xf.f,o=0;o<r.length;o++){var s=r[o];!Dn(a,s)&&!(t&&Dn(t,s))&&i(a,s,n(e,s))}},Sf=D,Ef=B,$f=/#|\.prototype\./,At=function(a,e){var t=Cf[wf(a)];return t===Pf?!0:t===Af?!1:Ef(e)?Sf(e):!!e},wf=At.normalize=function(a){return String(a).replace($f,".").toLowerCase()},Cf=At.data={},Af=At.NATIVE="N",Pf=At.POLYFILL="P",nu=At,zt=_,Rf=Et.f,Nf=Ct,If=Ue,Mf=Oi,_f=Of,Vf=nu,ee=function(a,e){var t=a.target,r=a.global,i=a.stat,n,o,s,u,l,h;if(r?o=zt:i?o=zt[t]||Mf(t,{}):o=zt[t]&&zt[t].prototype,o)for(s in e){if(l=e[s],a.dontCallGetSet?(h=Rf(o,s),u=h&&h.value):u=o[s],n=Vf(r?s:t+(i?".":"#")+s,a.forced),!n&&u!==void 0){if(typeof l==typeof u)continue;_f(l,u)}(a.sham||u&&u.sham)&&Nf(l,"sham",!0),If(o,s,l,a)}},vt=_,Df=wt,Lf=je,Ht=function(a){return Df.slice(0,a.length)===a},su=function(){return Ht("Bun/")?"BUN":Ht("Cloudflare-Workers")?"CLOUDFLARE":Ht("Deno/")?"DENO":Ht("Node.js/")?"NODE":vt.Bun&&typeof Bun.version=="string"?"BUN":vt.Deno&&typeof Deno.version=="object"?"DENO":Lf(vt.process)==="process"?"NODE":vt.window&&vt.document?"BROWSER":"REST"}(),kf=su,Ar=kf==="NODE",Bf=_,jf=Bf,Ff=L,Uf=Te,Gf=function(a,e,t){try{return Ff(Uf(Object.getOwnPropertyDescriptor(a,e)[t]))}catch{}},zf=ae,Hf=function(a){return zf(a)||a===null},Yf=Hf,Xf=String,Wf=TypeError,qf=function(a){if(Yf(a))return a;throw new Wf("Can't set "+Xf(a)+" as a prototype")},Qf=Gf,Kf=ae,Zf=ve,Jf=qf,ou=Object.setPrototypeOf||("__proto__"in{}?function(){var a=!1,e={},t;try{t=Qf(Object.prototype,"__proto__","set"),t(e,[]),a=e instanceof Array}catch{}return function(i,n){return Zf(i),Jf(n),Kf(i)&&(a?t(i,n):i.__proto__=n),i}}():void 0),ec=Oe.f,tc=fe,rc=z,Ln=rc("toStringTag"),Pr=function(a,e,t){a&&!t&&(a=a.prototype),a&&!tc(a,Ln)&&ec(a,Ln,{configurable:!0,value:e})},kn=eu,ac=Oe,ic=function(a,e,t){return t.get&&kn(t.get,e,{getter:!0}),t.set&&kn(t.set,e,{setter:!0}),ac.f(a,e,t)},nc=Fe,sc=ic,oc=z,uc=he,Bn=oc("species"),lc=function(a){var e=nc(a);uc&&e&&!e[Bn]&&sc(e,Bn,{configurable:!0,get:function(){return this}})},hc=Tr,vc=TypeError,fc=function(a,e){if(hc(e,a))return a;throw new vc("Incorrect invocation")},cc=z,gc=cc("toStringTag"),uu={};uu[gc]="z";var dc=String(uu)==="[object z]",pc=dc,yc=B,ur=je,mc=z,bc=mc("toStringTag"),xc=Object,Tc=ur(function(){return arguments}())==="Arguments",Oc=function(a,e){try{return a[e]}catch{}},Ni=pc?ur:function(a){var e,t,r;return a===void 0?"Undefined":a===null?"Null":typeof(t=Oc(e=xc(a),bc))=="string"?t:Tc?ur(e):(r=ur(e))==="Object"&&yc(e.callee)?"Arguments":r},Sc=L,Ec=D,lu=B,$c=Ni,wc=Fe,Cc=$i,hu=function(){},vu=wc("Reflect","construct"),Ii=/^\s*(?:class|function)\b/,Ac=Sc(Ii.exec),Pc=!Ii.test(hu),ft=function(e){if(!lu(e))return!1;try{return vu(hu,[],e),!0}catch{return!1}},fu=function(e){if(!lu(e))return!1;switch($c(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Pc||!!Ac(Ii,Cc(e))}catch{return!0}};fu.sham=!0;var Rc=!vu||Ec(function(){var a;return ft(ft.call)||!ft(Object)||!ft(function(){a=!0})||a})?fu:ft,Nc=Rc,Ic=Or,Mc=TypeError,_c=function(a){if(Nc(a))return a;throw new Mc(Ic(a)+" is not a constructor")},jn=J,Vc=_c,Dc=xr,Lc=z,kc=Lc("species"),cu=function(a,e){var t=jn(a).constructor,r;return t===void 0||Dc(r=jn(t)[kc])?e:Vc(r)},Bc=br,gu=Function.prototype,Fn=gu.apply,Un=gu.call,du=typeof Reflect=="object"&&Reflect.apply||(Bc?Un.bind(Fn):function(){return Un.apply(Fn,arguments)}),jc=je,Fc=L,Rr=function(a){if(jc(a)==="Function")return Fc(a)},Gn=Rr,Uc=Te,Gc=br,zc=Gn(Gn.bind),Mi=function(a,e){return Uc(a),e===void 0?a:Gc?zc(a,e):function(){return a.apply(e,arguments)}},Hc=Fe,pu=Hc("document","documentElement"),Yc=L,Xc=Yc([].slice),Wc=TypeError,qc=function(a,e){if(a<e)throw new Wc("Not enough arguments");return a},Qc=wt,yu=/(?:ipad|iphone|ipod).*applewebkit/i.test(Qc),ie=_,Kc=du,Zc=Mi,zn=B,Jc=fe,mu=D,Hn=pu,eg=Xc,Yn=Er,tg=qc,rg=yu,ag=Ar,Ka=ie.setImmediate,Xn=ie.clearImmediate,ig=ie.process,ca=ie.Dispatch,ng=ie.Function,Wn=ie.MessageChannel,sg=ie.String,ga=0,bt={},qn="onreadystatechange",Ot,Ie,da,pa;mu(function(){Ot=ie.location});var _i=function(a){if(Jc(bt,a)){var e=bt[a];delete bt[a],e()}},ya=function(a){return function(){_i(a)}},Qn=function(a){_i(a.data)},Kn=function(a){ie.postMessage(sg(a),Ot.protocol+"//"+Ot.host)};(!Ka||!Xn)&&(Ka=function(e){tg(arguments.length,1);var t=zn(e)?e:ng(e),r=eg(arguments,1);return bt[++ga]=function(){Kc(t,void 0,r)},Ie(ga),ga},Xn=function(e){delete bt[e]},ag?Ie=function(a){ig.nextTick(ya(a))}:ca&&ca.now?Ie=function(a){ca.now(ya(a))}:Wn&&!rg?(da=new Wn,pa=da.port2,da.port1.onmessage=Qn,Ie=Zc(pa.postMessage,pa)):ie.addEventListener&&zn(ie.postMessage)&&!ie.importScripts&&Ot&&Ot.protocol!=="file:"&&!mu(Kn)?(Ie=Kn,ie.addEventListener("message",Qn,!1)):qn in Yn("script")?Ie=function(a){Hn.appendChild(Yn("script"))[qn]=function(){Hn.removeChild(this),_i(a)}}:Ie=function(a){setTimeout(ya(a),0)});var bu={set:Ka},Zn=_,og=he,ug=Object.getOwnPropertyDescriptor,lg=function(a){if(!og)return Zn[a];var e=ug(Zn,a);return e&&e.value},xu=function(){this.head=null,this.tail=null};xu.prototype={add:function(a){var e={item:a,next:null},t=this.tail;t?t.next=e:this.head=e,this.tail=e},get:function(){var a=this.head;if(a){var e=this.head=a.next;return e===null&&(this.tail=null),a.item}}};var Tu=xu,hg=wt,vg=/ipad|iphone|ipod/i.test(hg)&&typeof Pebble<"u",fg=wt,cg=/web0s(?!.*chrome)/i.test(fg),at=_,gg=lg,Jn=Mi,ma=bu.set,dg=Tu,pg=yu,yg=vg,mg=cg,ba=Ar,es=at.MutationObserver||at.WebKitMutationObserver,ts=at.document,rs=at.process,Yt=at.Promise,Za=gg("queueMicrotask"),Xe,xa,Ta,Xt,as;if(!Za){var Wt=new dg,qt=function(){var a,e;for(ba&&(a=rs.domain)&&a.exit();e=Wt.get();)try{e()}catch(t){throw Wt.head&&Xe(),t}a&&a.enter()};!pg&&!ba&&!mg&&es&&ts?(xa=!0,Ta=ts.createTextNode(""),new es(qt).observe(Ta,{characterData:!0}),Xe=function(){Ta.data=xa=!xa}):!yg&&Yt&&Yt.resolve?(Xt=Yt.resolve(void 0),Xt.constructor=Yt,as=Jn(Xt.then,Xt),Xe=function(){as(qt)}):ba?Xe=function(){rs.nextTick(qt)}:(ma=Jn(ma,at),Xe=function(){ma(qt)}),Za=function(a){Wt.head||Xe(),Wt.add(a)}}var bg=Za,xg=function(a,e){try{arguments.length===1?console.error(a):console.error(a,e)}catch{}},Vi=function(a){try{return{error:!1,value:a()}}catch(e){return{error:!0,value:e}}},Tg=_,Nr=Tg.Promise,Og=_,xt=Nr,Sg=B,Eg=nu,$g=$i,wg=z,is=su,Oa=Ti;xt&&xt.prototype;var Cg=wg("species"),Ja=!1,Ou=Sg(Og.PromiseRejectionEvent),Ag=Eg("Promise",function(){var a=$g(xt),e=a!==String(xt);if(!e&&Oa===66)return!0;if(!Oa||Oa<51||!/native code/.test(a)){var t=new xt(function(n){n(1)}),r=function(n){n(function(){},function(){})},i=t.constructor={};if(i[Cg]=r,Ja=t.then(function(){})instanceof r,!Ja)return!0}return!e&&(is==="BROWSER"||is==="DENO")&&!Ou}),Pt={CONSTRUCTOR:Ag,REJECTION_EVENT:Ou,SUBCLASSING:Ja},ut={},ns=Te,Pg=TypeError,Rg=function(a){var e,t;this.promise=new a(function(r,i){if(e!==void 0||t!==void 0)throw new Pg("Bad Promise constructor");e=r,t=i}),this.resolve=ns(e),this.reject=ns(t)};ut.f=function(a){return new Rg(a)};var Ng=ee,pr=Ar,we=_,Ig=jf,it=Y,ss=Ue,os=ou,Mg=Pr,_g=lc,Vg=Te,lr=B,Dg=ae,Lg=fc,kg=cu,Su=bu.set,Di=bg,Bg=xg,jg=Vi,Fg=Tu,Eu=wr,yr=Nr,Li=Pt,$u=ut,Ir="Promise",wu=Li.CONSTRUCTOR,Ug=Li.REJECTION_EVENT,Gg=Li.SUBCLASSING,Sa=Eu.getterFor(Ir),zg=Eu.set,qe=yr&&yr.prototype,Le=yr,Qt=qe,Cu=we.TypeError,ei=we.document,ki=we.process,ti=$u.f,Hg=ti,Yg=!!(ei&&ei.createEvent&&we.dispatchEvent),Au="unhandledrejection",Xg="rejectionhandled",us=0,Pu=1,Wg=2,Bi=1,Ru=2,Kt,ls,Nu,hs,Iu=function(a){var e;return Dg(a)&&lr(e=a.then)?e:!1},Mu=function(a,e){var t=e.value,r=e.state===Pu,i=r?a.ok:a.fail,n=a.resolve,o=a.reject,s=a.domain,u,l,h;try{i?(r||(e.rejection===Ru&&Qg(e),e.rejection=Bi),i===!0?u=t:(s&&s.enter(),u=i(t),s&&(s.exit(),h=!0)),u===a.promise?o(new Cu("Promise-chain cycle")):(l=Iu(u))?it(l,u,n,o):n(u)):o(t)}catch(f){s&&!h&&s.exit(),o(f)}},_u=function(a,e){a.notified||(a.notified=!0,Di(function(){for(var t=a.reactions,r;r=t.get();)Mu(r,a);a.notified=!1,e&&!a.rejection&&qg(a)}))},Vu=function(a,e,t){var r,i;Yg?(r=ei.createEvent("Event"),r.promise=e,r.reason=t,r.initEvent(a,!1,!0),we.dispatchEvent(r)):r={promise:e,reason:t},!Ug&&(i=we["on"+a])?i(r):a===Au&&Bg("Unhandled promise rejection",t)},qg=function(a){it(Su,we,function(){var e=a.facade,t=a.value,r=vs(a),i;if(r&&(i=jg(function(){pr?ki.emit("unhandledRejection",t,e):Vu(Au,e,t)}),a.rejection=pr||vs(a)?Ru:Bi,i.error))throw i.value})},vs=function(a){return a.rejection!==Bi&&!a.parent},Qg=function(a){it(Su,we,function(){var e=a.facade;pr?ki.emit("rejectionHandled",e):Vu(Xg,e,a.value)})},Ze=function(a,e,t){return function(r){a(e,r,t)}},et=function(a,e,t){a.done||(a.done=!0,t&&(a=t),a.value=e,a.state=Wg,_u(a,!0))},ri=function(a,e,t){if(!a.done){a.done=!0,t&&(a=t);try{if(a.facade===e)throw new Cu("Promise can't be resolved itself");var r=Iu(e);r?Di(function(){var i={done:!1};try{it(r,e,Ze(ri,i,a),Ze(et,i,a))}catch(n){et(i,n,a)}}):(a.value=e,a.state=Pu,_u(a,!1))}catch(i){et({done:!1},i,a)}}};if(wu&&(Le=function(e){Lg(this,Qt),Vg(e),it(Kt,this);var t=Sa(this);try{e(Ze(ri,t),Ze(et,t))}catch(r){et(t,r)}},Qt=Le.prototype,Kt=function(e){zg(this,{type:Ir,done:!1,notified:!1,parent:!1,reactions:new Fg,rejection:!1,state:us,value:null})},Kt.prototype=ss(Qt,"then",function(e,t){var r=Sa(this),i=ti(kg(this,Le));return r.parent=!0,i.ok=lr(e)?e:!0,i.fail=lr(t)&&t,i.domain=pr?ki.domain:void 0,r.state===us?r.reactions.add(i):Di(function(){Mu(i,r)}),i.promise}),ls=function(){var a=new Kt,e=Sa(a);this.promise=a,this.resolve=Ze(ri,e),this.reject=Ze(et,e)},$u.f=ti=function(a){return a===Le||a===Nu?new ls(a):Hg(a)},lr(yr)&&qe!==Object.prototype)){hs=qe.then,Gg||ss(qe,"then",function(e,t){var r=this;return new Le(function(i,n){it(hs,r,i,n)}).then(e,t)},{unsafe:!0});try{delete qe.constructor}catch{}os&&os(qe,Qt)}Ng({global:!0,constructor:!0,wrap:!0,forced:wu},{Promise:Le});Nu=Ig.Promise;Mg(Le,Ir,!1);_g(Ir);var Rt={},Kg=z,Zg=Rt,Jg=Kg("iterator"),ed=Array.prototype,td=function(a){return a!==void 0&&(Zg.Array===a||ed[Jg]===a)},rd=Ni,fs=st,ad=xr,id=Rt,nd=z,sd=nd("iterator"),Du=function(a){if(!ad(a))return fs(a,sd)||fs(a,"@@iterator")||id[rd(a)]},od=Y,ud=Te,ld=J,hd=Or,vd=Du,fd=TypeError,cd=function(a,e){var t=arguments.length<2?vd(a):e;if(ud(t))return ld(od(t,a));throw new fd(hd(a)+" is not iterable")},gd=Y,cs=J,dd=st,pd=function(a,e,t){var r,i;cs(a);try{if(r=dd(a,"return"),!r){if(e==="throw")throw t;return t}r=gd(r,a)}catch(n){i=!0,r=n}if(e==="throw")throw t;if(i)throw r;return cs(r),t},yd=Mi,md=Y,bd=J,xd=Or,Td=td,Od=Pi,gs=Tr,Sd=cd,Ed=Du,ds=pd,$d=TypeError,hr=function(a,e){this.stopped=a,this.result=e},ps=hr.prototype,Lu=function(a,e,t){var r=t&&t.that,i=!!(t&&t.AS_ENTRIES),n=!!(t&&t.IS_RECORD),o=!!(t&&t.IS_ITERATOR),s=!!(t&&t.INTERRUPTED),u=yd(e,r),l,h,f,c,v,g,d,p=function(x){return l&&ds(l,"normal"),new hr(!0,x)},y=function(x){return i?(bd(x),s?u(x[0],x[1],p):u(x[0],x[1])):s?u(x,p):u(x)};if(n)l=a.iterator;else if(o)l=a;else{if(h=Ed(a),!h)throw new $d(xd(a)+" is not iterable");if(Td(h)){for(f=0,c=Od(a);c>f;f++)if(v=y(a[f]),v&&gs(ps,v))return v;return new hr(!1)}l=Sd(a,h)}for(g=n?a.next:l.next;!(d=md(g,l)).done;){try{v=y(d.value)}catch(x){ds(l,"throw",x)}if(typeof v=="object"&&v&&gs(ps,v))return v}return new hr(!1)},wd=z,ku=wd("iterator"),Bu=!1;try{var Cd=0,ys={next:function(){return{done:!!Cd++}},return:function(){Bu=!0}};ys[ku]=function(){return this},Array.from(ys,function(){throw 2})}catch{}var Ad=function(a,e){try{if(!e&&!Bu)return!1}catch{return!1}var t=!1;try{var r={};r[ku]=function(){return{next:function(){return{done:t=!0}}}},a(r)}catch{}return t},Pd=Nr,Rd=Ad,Nd=Pt.CONSTRUCTOR,ju=Nd||!Rd(function(a){Pd.all(a).then(void 0,function(){})}),Id=ee,Md=Y,_d=Te,Vd=ut,Dd=Vi,Ld=Lu,kd=ju;Id({target:"Promise",stat:!0,forced:kd},{all:function(e){var t=this,r=Vd.f(t),i=r.resolve,n=r.reject,o=Dd(function(){var s=_d(t.resolve),u=[],l=0,h=1;Ld(e,function(f){var c=l++,v=!1;h++,Md(s,t,f).then(function(g){v||(v=!0,u[c]=g,--h||i(u))},n)}),--h||i(u)});return o.error&&n(o.value),r.promise}});var Bd=ee,jd=Pt.CONSTRUCTOR,ai=Nr,Fd=Fe,Ud=B,Gd=Ue,ms=ai&&ai.prototype;Bd({target:"Promise",proto:!0,forced:jd,real:!0},{catch:function(a){return this.then(void 0,a)}});if(Ud(ai)){var bs=Fd("Promise").prototype.catch;ms.catch!==bs&&Gd(ms,"catch",bs,{unsafe:!0})}var zd=ee,Hd=Y,Yd=Te,Xd=ut,Wd=Vi,qd=Lu,Qd=ju;zd({target:"Promise",stat:!0,forced:Qd},{race:function(e){var t=this,r=Xd.f(t),i=r.reject,n=Wd(function(){var o=Yd(t.resolve);qd(e,function(s){Hd(o,t,s).then(r.resolve,i)})});return n.error&&i(n.value),r.promise}});var Kd=ee,Zd=ut,Jd=Pt.CONSTRUCTOR;Kd({target:"Promise",stat:!0,forced:Jd},{reject:function(e){var t=Zd.f(this),r=t.reject;return r(e),t.promise}});var ep=J,tp=ae,rp=ut,ap=function(a,e){if(ep(a),tp(e)&&e.constructor===a)return e;var t=rp.f(a),r=t.resolve;return r(e),t.promise},ip=ee,np=Fe,sp=Pt.CONSTRUCTOR,op=ap;np("Promise");ip({target:"Promise",stat:!0,forced:sp},{resolve:function(e){return op(this,e)}});function xs(a,e,t,r,i,n,o){try{var s=a[n](o),u=s.value}catch(l){return void t(l)}s.done?e(u):Promise.resolve(u).then(r,i)}function xe(a){return function(){var e=this,t=arguments;return new Promise(function(r,i){var n=a.apply(e,t);function o(u){xs(n,r,i,o,s,"next",u)}function s(u){xs(n,r,i,o,s,"throw",u)}o(void 0)})}}var up=Ni,lp=String,pe=function(a){if(up(a)==="Symbol")throw new TypeError("Cannot convert a Symbol value to a string");return lp(a)},hp=J,Fu=function(){var a=hp(this),e="";return a.hasIndices&&(e+="d"),a.global&&(e+="g"),a.ignoreCase&&(e+="i"),a.multiline&&(e+="m"),a.dotAll&&(e+="s"),a.unicode&&(e+="u"),a.unicodeSets&&(e+="v"),a.sticky&&(e+="y"),e},ji=D,vp=_,Fi=vp.RegExp,Ui=ji(function(){var a=Fi("a","y");return a.lastIndex=2,a.exec("abcd")!==null});Ui||ji(function(){return!Fi("a","y").sticky});var fp=Ui||ji(function(){var a=Fi("^r","gy");return a.lastIndex=2,a.exec("str")!==null}),Uu={BROKEN_CARET:fp,UNSUPPORTED_Y:Ui},Gu={},cp=au,gp=Ri,dp=Object.keys||function(e){return cp(e,gp)},pp=he,yp=Wo,mp=Oe,bp=J,xp=$t,Tp=dp;Gu.f=pp&&!yp?Object.defineProperties:function(e,t){bp(e);for(var r=xp(t),i=Tp(t),n=i.length,o=0,s;n>o;)mp.f(e,s=i[o++],r[s]);return e};var Op=J,Sp=Gu,Ts=Ri,Ep=Ci,$p=pu,wp=Er,Cp=wi,Os=">",Ss="<",ii="prototype",ni="script",zu=Cp("IE_PROTO"),Ea=function(){},Hu=function(a){return Ss+ni+Os+a+Ss+"/"+ni+Os},Es=function(a){a.write(Hu("")),a.close();var e=a.parentWindow.Object;return a=null,e},Ap=function(){var a=wp("iframe"),e="java"+ni+":",t;return a.style.display="none",$p.appendChild(a),a.src=String(e),t=a.contentWindow.document,t.open(),t.write(Hu("document.F=Object")),t.close(),t.F},Zt,vr=function(){try{Zt=new ActiveXObject("htmlfile")}catch{}vr=typeof document<"u"?document.domain&&Zt?Es(Zt):Ap():Es(Zt);for(var a=Ts.length;a--;)delete vr[ii][Ts[a]];return vr()};Ep[zu]=!0;var Gi=Object.create||function(e,t){var r;return e!==null?(Ea[ii]=Op(e),r=new Ea,Ea[ii]=null,r[zu]=e):r=vr(),t===void 0?r:Sp.f(r,t)},Pp=D,Rp=_,Np=Rp.RegExp,Ip=Pp(function(){var a=Np(".","s");return!(a.dotAll&&a.test(`
`)&&a.flags==="s")}),Mp=D,_p=_,Vp=_p.RegExp,Dp=Mp(function(){var a=Vp("(?<a>b)","g");return a.exec("b").groups.a!=="b"||"b".replace(a,"$<a>c")!=="bc"}),Je=Y,Mr=L,Lp=pe,kp=Fu,Bp=Uu,jp=Ei,Fp=Gi,Up=wr.get,Gp=Ip,zp=Dp,Hp=jp("native-string-replace",String.prototype.replace),mr=RegExp.prototype.exec,si=mr,Yp=Mr("".charAt),Xp=Mr("".indexOf),Wp=Mr("".replace),$a=Mr("".slice),oi=function(){var a=/a/,e=/b*/g;return Je(mr,a,"a"),Je(mr,e,"a"),a.lastIndex!==0||e.lastIndex!==0}(),Yu=Bp.BROKEN_CARET,ui=/()??/.exec("")[1]!==void 0,qp=oi||ui||Yu||Gp||zp;qp&&(si=function(e){var t=this,r=Up(t),i=Lp(e),n=r.raw,o,s,u,l,h,f,c;if(n)return n.lastIndex=t.lastIndex,o=Je(si,n,i),t.lastIndex=n.lastIndex,o;var v=r.groups,g=Yu&&t.sticky,d=Je(kp,t),p=t.source,y=0,x=i;if(g&&(d=Wp(d,"y",""),Xp(d,"g")===-1&&(d+="g"),x=$a(i,t.lastIndex),t.lastIndex>0&&(!t.multiline||t.multiline&&Yp(i,t.lastIndex-1)!==`
`)&&(p="(?: "+p+")",x=" "+x,y++),s=new RegExp("^(?:"+p+")",d)),ui&&(s=new RegExp("^"+p+"$(?!\\s)",d)),oi&&(u=t.lastIndex),l=Je(mr,g?s:t,x),g?l?(l.input=$a(l.input,y),l[0]=$a(l[0],y),l.index=t.lastIndex,t.lastIndex+=l[0].length):t.lastIndex=0:oi&&l&&(t.lastIndex=t.global?l.index+l[0].length:u),ui&&l&&l.length>1&&Je(Hp,l[0],s,function(){for(h=1;h<arguments.length-2;h++)arguments[h]===void 0&&(l[h]=void 0)}),l&&v)for(l.groups=f=Fp(null),h=0;h<v.length;h++)c=v[h],f[c[0]]=l[c[1]];return l});var zi=si,Qp=ee,$s=zi;Qp({target:"RegExp",proto:!0,forced:/./.exec!==$s},{exec:$s});var ws=Y,Cs=Ue,Kp=zi,As=D,Xu=z,Zp=Ct,Jp=Xu("species"),wa=RegExp.prototype,Hi=function(a,e,t,r){var i=Xu(a),n=!As(function(){var l={};return l[i]=function(){return 7},""[a](l)!==7}),o=n&&!As(function(){var l=!1,h=/a/;return a==="split"&&(h={},h.constructor={},h.constructor[Jp]=function(){return h},h.flags="",h[i]=/./[i]),h.exec=function(){return l=!0,null},h[i](""),!l});if(!n||!o||t){var s=/./[i],u=e(i,""[a],function(l,h,f,c,v){var g=h.exec;return g===Kp||g===wa.exec?n&&!v?{done:!0,value:ws(s,h,f,c)}:{done:!0,value:ws(l,f,h,c)}:{done:!1}});Cs(String.prototype,a,u[0]),Cs(wa,i,u[1])}r&&Zp(wa[i],"sham",!0)},Yi=L,ey=Cr,ty=pe,ry=ve,ay=Yi("".charAt),Ps=Yi("".charCodeAt),iy=Yi("".slice),ny=function(a){return function(e,t){var r=ty(ry(e)),i=ey(t),n=r.length,o,s;return i<0||i>=n?a?"":void 0:(o=Ps(r,i),o<55296||o>56319||i+1===n||(s=Ps(r,i+1))<56320||s>57343?a?ay(r,i):o:a?iy(r,i,i+2):(o-55296<<10)+(s-56320)+65536)}},sy={charAt:ny(!0)},oy=sy.charAt,Xi=function(a,e,t){return e+(t?oy(a,e).length:1)},uy=_,ly=D,Rs=uy.RegExp,hy=!ly(function(){var a=!0;try{Rs(".","d")}catch{a=!1}var e={},t="",r=a?"dgimsy":"gimsy",i=function(u,l){Object.defineProperty(e,u,{get:function(){return t+=l,!0}})},n={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};a&&(n.hasIndices="d");for(var o in n)i(o,n[o]);var s=Object.getOwnPropertyDescriptor(Rs.prototype,"flags").get.call(e);return s!==r||t!==r}),vy={correct:hy},fy=Y,cy=fe,gy=Tr,Ns=vy,dy=Fu,py=RegExp.prototype,Wi=Ns.correct?function(a){return a.flags}:function(a){return!Ns.correct&&gy(py,a)&&!cy(a,"flags")?fy(dy,a):a.flags},Is=Y,yy=J,my=B,by=je,xy=zi,Ty=TypeError,qi=function(a,e){var t=a.exec;if(my(t)){var r=Is(t,a,e);return r!==null&&yy(r),r}if(by(a)==="RegExp")return Is(xy,a,e);throw new Ty("RegExp#exec called on incompatible receiver")},Oy=Y,Sy=L,Ey=Hi,$y=J,wy=ae,Cy=ot,Jt=pe,Ay=ve,Py=st,Ry=Xi,Ny=Wi,Ms=qi,_s=Sy("".indexOf);Ey("match",function(a,e,t){return[function(i){var n=Ay(this),o=wy(i)?Py(i,a):void 0;return o?Oy(o,i,n):new RegExp(i)[a](Jt(n))},function(r){var i=$y(this),n=Jt(r),o=t(e,i,n);if(o.done)return o.value;var s=Jt(Ny(i));if(_s(s,"g")===-1)return Ms(i,n);var u=_s(s,"u")!==-1;i.lastIndex=0;for(var l=[],h=0,f;(f=Ms(i,n))!==null;){var c=Jt(f[0]);l[h]=c,c===""&&(i.lastIndex=Ry(n,Cy(i.lastIndex),u)),h++}return h===0?null:l}]});var Qi=L,Iy=Sr,My=Math.floor,Ca=Qi("".charAt),_y=Qi("".replace),Aa=Qi("".slice),Vy=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Dy=/\$([$&'`]|\d{1,2})/g,Ly=function(a,e,t,r,i,n){var o=t+a.length,s=r.length,u=Dy;return i!==void 0&&(i=Iy(i),u=Vy),_y(n,u,function(l,h){var f;switch(Ca(h,0)){case"$":return"$";case"&":return a;case"`":return Aa(e,0,t);case"'":return Aa(e,o);case"<":f=i[Aa(h,1,-1)];break;default:var c=+h;if(c===0)return l;if(c>s){var v=My(c/10);return v===0?l:v<=s?r[v-1]===void 0?Ca(h,1):r[v-1]+Ca(h,1):l}f=r[c-1]}return f===void 0?"":f})},ky=du,Vs=Y,_r=L,By=Hi,jy=D,Fy=J,Uy=B,Gy=ae,zy=Cr,Hy=ot,Me=pe,Yy=ve,Xy=Xi,Wy=st,qy=Ly,Qy=Wi,Ky=qi,Zy=z,li=Zy("replace"),Jy=Math.max,em=Math.min,tm=_r([].concat),Pa=_r([].push),er=_r("".indexOf),Ds=_r("".slice),rm=function(a){return a===void 0?a:String(a)},am=function(){return"a".replace(/./,"$0")==="$0"}(),Ls=function(){return/./[li]?/./[li]("a","$0")==="":!1}(),im=!jy(function(){var a=/./;return a.exec=function(){var e=[];return e.groups={a:"7"},e},"".replace(a,"$<a>")!=="7"});By("replace",function(a,e,t){var r=Ls?"$":"$0";return[function(n,o){var s=Yy(this),u=Gy(n)?Wy(n,li):void 0;return u?Vs(u,n,s,o):Vs(e,Me(s),n,o)},function(i,n){var o=Fy(this),s=Me(i);if(typeof n=="string"&&er(n,r)===-1&&er(n,"$<")===-1){var u=t(e,o,s,n);if(u.done)return u.value}var l=Uy(n);l||(n=Me(n));var h=Me(Qy(o)),f=er(h,"g")!==-1,c;f&&(c=er(h,"u")!==-1,o.lastIndex=0);for(var v=[],g;g=Ky(o,s),!(g===null||(Pa(v,g),!f));){var d=Me(g[0]);d===""&&(o.lastIndex=Xy(s,Hy(o.lastIndex),c))}for(var p="",y=0,x=0;x<v.length;x++){g=v[x];for(var b=Me(g[0]),T=Jy(em(zy(g.index),s.length),0),$=[],E,O=1;O<g.length;O++)Pa($,rm(g[O]));var C=g.groups;if(l){var P=tm([b],$,T,s);C!==void 0&&Pa(P,C),E=Me(ky(n,void 0,P))}else E=qy(b,s,T,$,C,n);T>=y&&(p+=Ds(s,y,T)+E,y=T+b.length)}return p+Ds(s,y)}]},!im||!am||Ls);var nm=ae,sm=je,om=z,um=om("match"),lm=function(a){var e;return nm(a)&&((e=a[um])!==void 0?!!e:sm(a)==="RegExp")},hm=lm,vm=TypeError,Ki=function(a){if(hm(a))throw new vm("The method doesn't accept regular expressions");return a},fm=z,cm=fm("match"),Zi=function(a){var e=/./;try{"/./"[a](e)}catch{try{return e[cm]=!1,"/./"[a](e)}catch{}}return!1},gm=ee,dm=Rr,pm=Et.f,ym=ot,ks=pe,mm=Ki,bm=ve,xm=Zi,Tm=dm("".slice),Om=Math.min,Wu=xm("startsWith"),Sm=!Wu&&!!function(){var a=pm(String.prototype,"startsWith");return a&&!a.writable}();gm({target:"String",proto:!0,forced:!Sm&&!Wu},{startsWith:function(e){var t=ks(bm(this));mm(e);var r=ym(Om(arguments.length>1?arguments[1]:void 0,t.length)),i=ks(e);return Tm(t,r,r+i.length)===i}});var Em=z,$m=Gi,wm=Oe.f,hi=Em("unscopables"),vi=Array.prototype;vi[hi]===void 0&&wm(vi,hi,{configurable:!0,value:$m(null)});var Cm=function(a){vi[hi][a]=!0},Am=D,Pm=!Am(function(){function a(){}return a.prototype.constructor=null,Object.getPrototypeOf(new a)!==a.prototype}),Rm=fe,Nm=B,Im=Sr,Mm=wi,_m=Pm,Bs=Mm("IE_PROTO"),fi=Object,Vm=fi.prototype,qu=_m?fi.getPrototypeOf:function(a){var e=Im(a);if(Rm(e,Bs))return e[Bs];var t=e.constructor;return Nm(t)&&e instanceof t?t.prototype:e instanceof fi?Vm:null},Dm=D,Lm=B,km=ae,js=qu,Bm=Ue,jm=z,ci=jm("iterator"),Qu=!1,ke,Ra,Na;[].keys&&(Na=[].keys(),"next"in Na?(Ra=js(js(Na)),Ra!==Object.prototype&&(ke=Ra)):Qu=!0);var Fm=!km(ke)||Dm(function(){var a={};return ke[ci].call(a)!==a});Fm&&(ke={});Lm(ke[ci])||Bm(ke,ci,function(){return this});var Ku={IteratorPrototype:ke,BUGGY_SAFARI_ITERATORS:Qu},Um=Ku.IteratorPrototype,Gm=Gi,zm=xi,Hm=Pr,Ym=Rt,Xm=function(){return this},Wm=function(a,e,t,r){var i=e+" Iterator";return a.prototype=Gm(Um,{next:zm(+!r,t)}),Hm(a,i,!1),Ym[i]=Xm,a},qm=ee,Qm=Y,Zu=$r,Km=B,Zm=Wm,Fs=qu,Us=ou,Jm=Pr,e0=Ct,Ia=Ue,t0=z,r0=Rt,Ju=Ku,a0=Zu.PROPER,i0=Zu.CONFIGURABLE,Gs=Ju.IteratorPrototype,tr=Ju.BUGGY_SAFARI_ITERATORS,ct=t0("iterator"),zs="keys",gt="values",Hs="entries",n0=function(){return this},s0=function(a,e,t,r,i,n,o){Zm(t,e,r);var s=function(y){if(y===i&&c)return c;if(!tr&&y&&y in h)return h[y];switch(y){case zs:return function(){return new t(this,y)};case gt:return function(){return new t(this,y)};case Hs:return function(){return new t(this,y)}}return function(){return new t(this)}},u=e+" Iterator",l=!1,h=a.prototype,f=h[ct]||h["@@iterator"]||i&&h[i],c=!tr&&f||s(i),v=e==="Array"&&h.entries||f,g,d,p;if(v&&(g=Fs(v.call(new a)),g!==Object.prototype&&g.next&&(Fs(g)!==Gs&&(Us?Us(g,Gs):Km(g[ct])||Ia(g,ct,n0)),Jm(g,u,!0))),a0&&i===gt&&f&&f.name!==gt&&(i0?e0(h,"name",gt):(l=!0,c=function(){return Qm(f,this)})),i)if(d={values:s(gt),keys:n?c:s(zs),entries:s(Hs)},o)for(p in d)(tr||l||!(p in h))&&Ia(h,p,d[p]);else qm({target:e,proto:!0,forced:tr||l},d);return h[ct]!==c&&Ia(h,ct,c,{name:i}),r0[e]=c,d},o0=function(a,e){return{value:a,done:e}},u0=$t,Ji=Cm,Ys=Rt,el=wr,l0=Oe.f,h0=s0,rr=o0,v0=he,tl="Array Iterator",f0=el.set,c0=el.getterFor(tl),g0=h0(Array,"Array",function(a,e){f0(this,{type:tl,target:u0(a),index:0,kind:e})},function(){var a=c0(this),e=a.target,t=a.index++;if(!e||t>=e.length)return a.target=null,rr(void 0,!0);switch(a.kind){case"keys":return rr(t,!1);case"values":return rr(e[t],!1)}return rr([t,e[t]],!1)},"values"),Xs=Ys.Arguments=Ys.Array;Ji("keys");Ji("values");Ji("entries");if(v0&&Xs.name!=="values")try{l0(Xs,"name",{value:"values"})}catch{}var d0={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},p0=Er,Ma=p0("span").classList,Ws=Ma&&Ma.constructor&&Ma.constructor.prototype,y0=Ws===Object.prototype?void 0:Ws,qs=_,rl=d0,m0=y0,pt=g0,Qs=Ct,b0=Pr,x0=z,_a=x0("iterator"),Va=pt.values,al=function(a,e){if(a){if(a[_a]!==Va)try{Qs(a,_a,Va)}catch{a[_a]=Va}if(b0(a,e,!0),rl[e]){for(var t in pt)if(a[t]!==pt[t])try{Qs(a,t,pt[t])}catch{a[t]=pt[t]}}}};for(var Da in rl)al(qs[Da]&&qs[Da].prototype,Da);al(m0,"DOMTokenList");function T0(a,e){if(za(a)!="object"||!a)return a;var t=a[Symbol.toPrimitive];if(t!==void 0){var r=t.call(a,e);if(za(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(a)}function O0(a){var e=T0(a,"string");return za(e)=="symbol"?e:e+""}function en(a,e,t){return(e=O0(e))in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}var S0=Te,E0=Sr,$0=Bo,w0=Pi,Ks=TypeError,Zs="Reduce of empty array with no initial value",C0=function(a){return function(e,t,r,i){var n=E0(e),o=$0(n),s=w0(n);if(S0(t),s===0&&r<2)throw new Ks(Zs);var u=a?s-1:0,l=a?-1:1;if(r<2)for(;;){if(u in o){i=o[u],u+=l;break}if(u+=l,a?u<0:s<=u)throw new Ks(Zs)}for(;a?u>=0:s>u;u+=l)u in o&&(i=t(i,o[u],u,n));return i}},A0={left:C0(!1)},P0=D,il=function(a,e){var t=[][a];return!!t&&P0(function(){t.call(null,e||function(){return 1},1)})},R0=ee,N0=A0.left,I0=il,Js=Ti,M0=Ar,_0=!M0&&Js>79&&Js<83,V0=_0||!I0("reduce");R0({target:"Array",proto:!0,forced:V0},{reduce:function(e){var t=arguments.length;return N0(this,e,t,t>1?arguments[1]:void 0)}});var D0=ee,L0=Rr,k0=Et.f,B0=ot,eo=pe,j0=Ki,F0=ve,U0=Zi,G0=L0("".slice),z0=Math.min,nl=U0("endsWith"),H0=!nl&&!!function(){var a=k0(String.prototype,"endsWith");return a&&!a.writable}();D0({target:"String",proto:!0,forced:!H0&&!nl},{endsWith:function(e){var t=eo(F0(this));j0(e);var r=arguments.length>1?arguments[1]:void 0,i=t.length,n=r===void 0?i:z0(B0(r),i),o=eo(e);return G0(t,n-o.length,n)===o}});var La=Y,sl=L,Y0=Hi,X0=J,W0=ae,q0=ve,Q0=cu,K0=Xi,Z0=ot,to=pe,J0=st,ro=qi,eb=Uu,tb=D,We=eb.UNSUPPORTED_Y,rb=4294967295,ab=Math.min,ka=sl([].push),Ba=sl("".slice),ib=!tb(function(){var a=/(?:)/,e=a.exec;a.exec=function(){return e.apply(this,arguments)};var t="ab".split(a);return t.length!==2||t[0]!=="a"||t[1]!=="b"}),ao="abbc".split(/(b)*/)[1]==="c"||"test".split(/(?:)/,-1).length!==4||"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||".".split(/()()/).length>1||"".split(/.?/).length;Y0("split",function(a,e,t){var r="0".split(void 0,0).length?function(i,n){return i===void 0&&n===0?[]:La(e,this,i,n)}:e;return[function(n,o){var s=q0(this),u=W0(n)?J0(n,a):void 0;return u?La(u,n,s,o):La(r,to(s),n,o)},function(i,n){var o=X0(this),s=to(i);if(!ao){var u=t(r,o,s,n,r!==e);if(u.done)return u.value}var l=Q0(o,RegExp),h=o.unicode,f=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(We?"g":"y"),c=new l(We?"^(?:"+o.source+")":o,f),v=n===void 0?rb:n>>>0;if(v===0)return[];if(s.length===0)return ro(c,s)===null?[s]:[];for(var g=0,d=0,p=[];d<s.length;){c.lastIndex=We?0:d;var y=ro(c,We?Ba(s,d):s),x;if(y===null||(x=ab(Z0(c.lastIndex+(We?d:0)),s.length))===g)d=K0(s,d,h);else{if(ka(p,Ba(s,g,d)),p.length===v)return p;for(var b=1;b<=y.length-1;b++)if(ka(p,y[b]),p.length===v)return p;d=g=x}}return ka(p,Ba(s,g)),p}]},ao||!ib,We);var Vr={exports:{}},yt={exports:{}};(function(){var a,e,t,r,i,n;typeof performance<"u"&&performance!==null&&performance.now?yt.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(yt.exports=function(){return(a()-i)/1e6},e=process.hrtime,a=function(){var o;return o=e(),o[0]*1e9+o[1]},r=a(),n=process.uptime()*1e9,i=r-n):Date.now?(yt.exports=function(){return Date.now()-t},t=Date.now()):(yt.exports=function(){return new Date().getTime()-t},t=new Date().getTime())}).call(Qe);var nb=yt.exports,sb=nb,be=typeof window>"u"?Qe:window,ar=["moz","webkit"],tt="AnimationFrame",nt=be["request"+tt],St=be["cancel"+tt]||be["cancelRequest"+tt];for(var dt=0;!nt&&dt<ar.length;dt++)nt=be[ar[dt]+"Request"+tt],St=be[ar[dt]+"Cancel"+tt]||be[ar[dt]+"CancelRequest"+tt];if(!nt||!St){var ja=0,io=0,_e=[],ob=1e3/60;nt=function(a){if(_e.length===0){var e=sb(),t=Math.max(0,ob-(e-ja));ja=t+e,setTimeout(function(){var r=_e.slice(0);_e.length=0;for(var i=0;i<r.length;i++)if(!r[i].cancelled)try{r[i].callback(ja)}catch(n){setTimeout(function(){throw n},0)}},Math.round(t))}return _e.push({handle:++io,callback:a,cancelled:!1}),io},St=function(a){for(var e=0;e<_e.length;e++)_e[e].handle===a&&(_e[e].cancelled=!0)}}Vr.exports=function(a){return nt.call(be,a)};Vr.exports.cancel=function(){St.apply(be,arguments)};Vr.exports.polyfill=function(a){a||(a=be),a.requestAnimationFrame=nt,a.cancelAnimationFrame=St};var ub=Vr.exports;const Fa=Io(ub);var ol=`	
\v\f\r                　\u2028\u2029\uFEFF`,lb=L,hb=ve,vb=pe,gi=ol,no=lb("".replace),fb=RegExp("^["+gi+"]+"),cb=RegExp("(^|[^"+gi+"])["+gi+"]+$"),gb=function(a){return function(e){var t=vb(hb(e));return a&1&&(t=no(t,fb,"")),a&2&&(t=no(t,cb,"$1")),t}},db={trim:gb(3)},pb=$r.PROPER,yb=D,so=ol,oo="​᠎",mb=function(a){return yb(function(){return!!so[a]()||oo[a]()!==oo||pb&&so[a].name!==a})},bb=ee,xb=db.trim,Tb=mb;bb({target:"String",proto:!0,forced:Tb("trim")},{trim:function(){return xb(this)}});var Ob=function(a){this.ok=!1,this.alpha=1,a.charAt(0)=="#"&&(a=a.substr(1,6)),a=a.replace(/ /g,""),a=a.toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};a=e[a]||a;for(var t=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3]),parseFloat(u[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(u){return[parseInt(u[1],16),parseInt(u[2],16),parseInt(u[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(u){return[parseInt(u[1]+u[1],16),parseInt(u[2]+u[2],16),parseInt(u[3]+u[3],16)]}}],r=0;r<t.length;r++){var i=t[r].re,n=t[r].process,o=i.exec(a);if(o){var s=n(o);this.r=s[0],this.g=s[1],this.b=s[2],s.length>3&&(this.alpha=s[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var u=this.r.toString(16),l=this.g.toString(16),h=this.b.toString(16);return u.length==1&&(u="0"+u),l.length==1&&(l="0"+l),h.length==1&&(h="0"+h),"#"+u+l+h},this.getHelpXML=function(){for(var u=new Array,l=0;l<t.length;l++)for(var h=t[l].example,f=0;f<h.length;f++)u[u.length]=h[f];for(var c in e)u[u.length]=c;var v=document.createElement("ul");v.setAttribute("id","rgbcolor-examples");for(var l=0;l<u.length;l++)try{var g=document.createElement("li"),d=new RGBColor(u[l]),p=document.createElement("div");p.style.cssText="margin: 3px; border: 1px solid black; background:"+d.toHex()+"; color:"+d.toHex(),p.appendChild(document.createTextNode("test"));var y=document.createTextNode(" "+u[l]+" -> "+d.toRGB()+" -> "+d.toHex());g.appendChild(p),g.appendChild(y),v.appendChild(g)}catch{}return v}};const di=Io(Ob);var Sb=ee,Eb=Rr,$b=ru.indexOf,wb=il,pi=Eb([].indexOf),ul=!!pi&&1/pi([1],1,-0)<0,Cb=ul||!wb("indexOf");Sb({target:"Array",proto:!0,forced:Cb},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return ul?pi(this,e,t)||0:$b(this,e,t)}});var Ab=ee,Pb=L,Rb=Ki,Nb=ve,uo=pe,Ib=Zi,Mb=Pb("".indexOf);Ab({target:"String",proto:!0,forced:!Ib("includes")},{includes:function(e){return!!~Mb(uo(Nb(this)),uo(Rb(e)),arguments.length>1?arguments[1]:void 0)}});var _b=je,Vb=Array.isArray||function(e){return _b(e)==="Array"},Db=ee,Lb=L,kb=Vb,Bb=Lb([].reverse),lo=[1,2];Db({target:"Array",proto:!0,forced:String(lo)===String(lo.reverse())},{reverse:function(){return kb(this)&&(this.length=this.length),Bb(this)}});/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var ll=function(a,e){return(ll=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])})(a,e)};function hl(a,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=a}ll(a,e),a.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}function jb(a){var e="";Array.isArray(a)||(a=[a]);for(var t=0;t<a.length;t++){var r=a[t];if(r.type===m.CLOSE_PATH)e+="z";else if(r.type===m.HORIZ_LINE_TO)e+=(r.relative?"h":"H")+r.x;else if(r.type===m.VERT_LINE_TO)e+=(r.relative?"v":"V")+r.y;else if(r.type===m.MOVE_TO)e+=(r.relative?"m":"M")+r.x+" "+r.y;else if(r.type===m.LINE_TO)e+=(r.relative?"l":"L")+r.x+" "+r.y;else if(r.type===m.CURVE_TO)e+=(r.relative?"c":"C")+r.x1+" "+r.y1+" "+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===m.SMOOTH_CURVE_TO)e+=(r.relative?"s":"S")+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===m.QUAD_TO)e+=(r.relative?"q":"Q")+r.x1+" "+r.y1+" "+r.x+" "+r.y;else if(r.type===m.SMOOTH_QUAD_TO)e+=(r.relative?"t":"T")+r.x+" "+r.y;else{if(r.type!==m.ARC)throw new Error('Unexpected command type "'+r.type+'" at index '+t+".");e+=(r.relative?"a":"A")+r.rX+" "+r.rY+" "+r.xRot+" "+ +r.lArcFlag+" "+ +r.sweepFlag+" "+r.x+" "+r.y}}return e}function yi(a,e){var t=a[0],r=a[1];return[t*Math.cos(e)-r*Math.sin(e),t*Math.sin(e)+r*Math.cos(e)]}function ue(){for(var a=[],e=0;e<arguments.length;e++)a[e]=arguments[e];for(var t=0;t<a.length;t++)if(typeof a[t]!="number")throw new Error("assertNumbers arguments["+t+"] is not a number. "+typeof a[t]+" == typeof "+a[t]);return!0}var Ee=Math.PI;function Ua(a,e,t){a.lArcFlag=a.lArcFlag===0?0:1,a.sweepFlag=a.sweepFlag===0?0:1;var r=a.rX,i=a.rY,n=a.x,o=a.y;r=Math.abs(a.rX),i=Math.abs(a.rY);var s=yi([(e-n)/2,(t-o)/2],-a.xRot/180*Ee),u=s[0],l=s[1],h=Math.pow(u,2)/Math.pow(r,2)+Math.pow(l,2)/Math.pow(i,2);1<h&&(r*=Math.sqrt(h),i*=Math.sqrt(h)),a.rX=r,a.rY=i;var f=Math.pow(r,2)*Math.pow(l,2)+Math.pow(i,2)*Math.pow(u,2),c=(a.lArcFlag!==a.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(r,2)*Math.pow(i,2)-f)/f)),v=r*l/i*c,g=-i*u/r*c,d=yi([v,g],a.xRot/180*Ee);a.cX=d[0]+(e+n)/2,a.cY=d[1]+(t+o)/2,a.phi1=Math.atan2((l-g)/i,(u-v)/r),a.phi2=Math.atan2((-l-g)/i,(-u-v)/r),a.sweepFlag===0&&a.phi2>a.phi1&&(a.phi2-=2*Ee),a.sweepFlag===1&&a.phi2<a.phi1&&(a.phi2+=2*Ee),a.phi1*=180/Ee,a.phi2*=180/Ee}function ho(a,e,t){ue(a,e,t);var r=a*a+e*e-t*t;if(0>r)return[];if(r===0)return[[a*t/(a*a+e*e),e*t/(a*a+e*e)]];var i=Math.sqrt(r);return[[(a*t+e*i)/(a*a+e*e),(e*t-a*i)/(a*a+e*e)],[(a*t-e*i)/(a*a+e*e),(e*t+a*i)/(a*a+e*e)]]}var U,ye=Math.PI/180;function vo(a,e,t){return(1-t)*a+t*e}function fo(a,e,t,r){return a+Math.cos(r/180*Ee)*e+Math.sin(r/180*Ee)*t}function co(a,e,t,r){var i=1e-6,n=e-a,o=t-e,s=3*n+3*(r-t)-6*o,u=6*(o-n),l=3*n;return Math.abs(s)<i?[-l/u]:function(h,f,c){var v=h*h/4-f;if(v<-c)return[];if(v<=c)return[-h/2];var g=Math.sqrt(v);return[-h/2-g,-h/2+g]}(u/s,l/s,i)}function go(a,e,t,r,i){var n=1-i;return a*(n*n*n)+e*(3*n*n*i)+t*(3*n*i*i)+r*(i*i*i)}(function(a){function e(){return i(function(s,u,l){return s.relative&&(s.x1!==void 0&&(s.x1+=u),s.y1!==void 0&&(s.y1+=l),s.x2!==void 0&&(s.x2+=u),s.y2!==void 0&&(s.y2+=l),s.x!==void 0&&(s.x+=u),s.y!==void 0&&(s.y+=l),s.relative=!1),s})}function t(){var s=NaN,u=NaN,l=NaN,h=NaN;return i(function(f,c,v){return f.type&m.SMOOTH_CURVE_TO&&(f.type=m.CURVE_TO,s=isNaN(s)?c:s,u=isNaN(u)?v:u,f.x1=f.relative?c-s:2*c-s,f.y1=f.relative?v-u:2*v-u),f.type&m.CURVE_TO?(s=f.relative?c+f.x2:f.x2,u=f.relative?v+f.y2:f.y2):(s=NaN,u=NaN),f.type&m.SMOOTH_QUAD_TO&&(f.type=m.QUAD_TO,l=isNaN(l)?c:l,h=isNaN(h)?v:h,f.x1=f.relative?c-l:2*c-l,f.y1=f.relative?v-h:2*v-h),f.type&m.QUAD_TO?(l=f.relative?c+f.x1:f.x1,h=f.relative?v+f.y1:f.y1):(l=NaN,h=NaN),f})}function r(){var s=NaN,u=NaN;return i(function(l,h,f){if(l.type&m.SMOOTH_QUAD_TO&&(l.type=m.QUAD_TO,s=isNaN(s)?h:s,u=isNaN(u)?f:u,l.x1=l.relative?h-s:2*h-s,l.y1=l.relative?f-u:2*f-u),l.type&m.QUAD_TO){s=l.relative?h+l.x1:l.x1,u=l.relative?f+l.y1:l.y1;var c=l.x1,v=l.y1;l.type=m.CURVE_TO,l.x1=((l.relative?0:h)+2*c)/3,l.y1=((l.relative?0:f)+2*v)/3,l.x2=(l.x+2*c)/3,l.y2=(l.y+2*v)/3}else s=NaN,u=NaN;return l})}function i(s){var u=0,l=0,h=NaN,f=NaN;return function(c){if(isNaN(h)&&!(c.type&m.MOVE_TO))throw new Error("path must start with moveto");var v=s(c,u,l,h,f);return c.type&m.CLOSE_PATH&&(u=h,l=f),c.x!==void 0&&(u=c.relative?u+c.x:c.x),c.y!==void 0&&(l=c.relative?l+c.y:c.y),c.type&m.MOVE_TO&&(h=u,f=l),v}}function n(s,u,l,h,f,c){return ue(s,u,l,h,f,c),i(function(v,g,d,p){var y=v.x1,x=v.x2,b=v.relative&&!isNaN(p),T=v.x!==void 0?v.x:b?0:g,$=v.y!==void 0?v.y:b?0:d;function E(se){return se*se}v.type&m.HORIZ_LINE_TO&&u!==0&&(v.type=m.LINE_TO,v.y=v.relative?0:d),v.type&m.VERT_LINE_TO&&l!==0&&(v.type=m.LINE_TO,v.x=v.relative?0:g),v.x!==void 0&&(v.x=v.x*s+$*l+(b?0:f)),v.y!==void 0&&(v.y=T*u+v.y*h+(b?0:c)),v.x1!==void 0&&(v.x1=v.x1*s+v.y1*l+(b?0:f)),v.y1!==void 0&&(v.y1=y*u+v.y1*h+(b?0:c)),v.x2!==void 0&&(v.x2=v.x2*s+v.y2*l+(b?0:f)),v.y2!==void 0&&(v.y2=x*u+v.y2*h+(b?0:c));var O=s*h-u*l;if(v.xRot!==void 0&&(s!==1||u!==0||l!==0||h!==1))if(O===0)delete v.rX,delete v.rY,delete v.xRot,delete v.lArcFlag,delete v.sweepFlag,v.type=m.LINE_TO;else{var C=v.xRot*Math.PI/180,P=Math.sin(C),V=Math.cos(C),j=1/E(v.rX),R=1/E(v.rY),X=E(V)*j+E(P)*R,W=2*P*V*(j-R),G=E(P)*j+E(V)*R,q=X*h*h-W*u*h+G*u*u,H=W*(s*h+u*l)-2*(X*l*h+G*s*u),Q=X*l*l-W*s*l+G*s*s,N=(Math.atan2(H,q-Q)+Math.PI)%Math.PI/2,M=Math.sin(N),K=Math.cos(N);v.rX=Math.abs(O)/Math.sqrt(q*E(K)+H*M*K+Q*E(M)),v.rY=Math.abs(O)/Math.sqrt(q*E(M)-H*M*K+Q*E(K)),v.xRot=180*N/Math.PI}return v.sweepFlag!==void 0&&0>O&&(v.sweepFlag=+!v.sweepFlag),v})}function o(){return function(s){var u={};for(var l in s)u[l]=s[l];return u}}a.ROUND=function(s){function u(l){return Math.round(l*s)/s}return s===void 0&&(s=1e13),ue(s),function(l){return l.x1!==void 0&&(l.x1=u(l.x1)),l.y1!==void 0&&(l.y1=u(l.y1)),l.x2!==void 0&&(l.x2=u(l.x2)),l.y2!==void 0&&(l.y2=u(l.y2)),l.x!==void 0&&(l.x=u(l.x)),l.y!==void 0&&(l.y=u(l.y)),l.rX!==void 0&&(l.rX=u(l.rX)),l.rY!==void 0&&(l.rY=u(l.rY)),l}},a.TO_ABS=e,a.TO_REL=function(){return i(function(s,u,l){return s.relative||(s.x1!==void 0&&(s.x1-=u),s.y1!==void 0&&(s.y1-=l),s.x2!==void 0&&(s.x2-=u),s.y2!==void 0&&(s.y2-=l),s.x!==void 0&&(s.x-=u),s.y!==void 0&&(s.y-=l),s.relative=!0),s})},a.NORMALIZE_HVZ=function(s,u,l){return s===void 0&&(s=!0),u===void 0&&(u=!0),l===void 0&&(l=!0),i(function(h,f,c,v,g){if(isNaN(v)&&!(h.type&m.MOVE_TO))throw new Error("path must start with moveto");return u&&h.type&m.HORIZ_LINE_TO&&(h.type=m.LINE_TO,h.y=h.relative?0:c),l&&h.type&m.VERT_LINE_TO&&(h.type=m.LINE_TO,h.x=h.relative?0:f),s&&h.type&m.CLOSE_PATH&&(h.type=m.LINE_TO,h.x=h.relative?v-f:v,h.y=h.relative?g-c:g),h.type&m.ARC&&(h.rX===0||h.rY===0)&&(h.type=m.LINE_TO,delete h.rX,delete h.rY,delete h.xRot,delete h.lArcFlag,delete h.sweepFlag),h})},a.NORMALIZE_ST=t,a.QT_TO_C=r,a.INFO=i,a.SANITIZE=function(s){s===void 0&&(s=0),ue(s);var u=NaN,l=NaN,h=NaN,f=NaN;return i(function(c,v,g,d,p){var y=Math.abs,x=!1,b=0,T=0;if(c.type&m.SMOOTH_CURVE_TO&&(b=isNaN(u)?0:v-u,T=isNaN(l)?0:g-l),c.type&(m.CURVE_TO|m.SMOOTH_CURVE_TO)?(u=c.relative?v+c.x2:c.x2,l=c.relative?g+c.y2:c.y2):(u=NaN,l=NaN),c.type&m.SMOOTH_QUAD_TO?(h=isNaN(h)?v:2*v-h,f=isNaN(f)?g:2*g-f):c.type&m.QUAD_TO?(h=c.relative?v+c.x1:c.x1,f=c.relative?g+c.y1:c.y2):(h=NaN,f=NaN),c.type&m.LINE_COMMANDS||c.type&m.ARC&&(c.rX===0||c.rY===0||!c.lArcFlag)||c.type&m.CURVE_TO||c.type&m.SMOOTH_CURVE_TO||c.type&m.QUAD_TO||c.type&m.SMOOTH_QUAD_TO){var $=c.x===void 0?0:c.relative?c.x:c.x-v,E=c.y===void 0?0:c.relative?c.y:c.y-g;b=isNaN(h)?c.x1===void 0?b:c.relative?c.x:c.x1-v:h-v,T=isNaN(f)?c.y1===void 0?T:c.relative?c.y:c.y1-g:f-g;var O=c.x2===void 0?0:c.relative?c.x:c.x2-v,C=c.y2===void 0?0:c.relative?c.y:c.y2-g;y($)<=s&&y(E)<=s&&y(b)<=s&&y(T)<=s&&y(O)<=s&&y(C)<=s&&(x=!0)}return c.type&m.CLOSE_PATH&&y(v-d)<=s&&y(g-p)<=s&&(x=!0),x?[]:c})},a.MATRIX=n,a.ROTATE=function(s,u,l){u===void 0&&(u=0),l===void 0&&(l=0),ue(s,u,l);var h=Math.sin(s),f=Math.cos(s);return n(f,h,-h,f,u-u*f+l*h,l-u*h-l*f)},a.TRANSLATE=function(s,u){return u===void 0&&(u=0),ue(s,u),n(1,0,0,1,s,u)},a.SCALE=function(s,u){return u===void 0&&(u=s),ue(s,u),n(s,0,0,u,0,0)},a.SKEW_X=function(s){return ue(s),n(1,0,Math.atan(s),1,0,0)},a.SKEW_Y=function(s){return ue(s),n(1,Math.atan(s),0,1,0,0)},a.X_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ue(s),n(-1,0,0,1,s,0)},a.Y_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ue(s),n(1,0,0,-1,0,s)},a.A_TO_C=function(){return i(function(s,u,l){return m.ARC===s.type?function(h,f,c){var v,g,d,p;h.cX||Ua(h,f,c);for(var y=Math.min(h.phi1,h.phi2),x=Math.max(h.phi1,h.phi2)-y,b=Math.ceil(x/90),T=new Array(b),$=f,E=c,O=0;O<b;O++){var C=vo(h.phi1,h.phi2,O/b),P=vo(h.phi1,h.phi2,(O+1)/b),V=P-C,j=4/3*Math.tan(V*ye/4),R=[Math.cos(C*ye)-j*Math.sin(C*ye),Math.sin(C*ye)+j*Math.cos(C*ye)],X=R[0],W=R[1],G=[Math.cos(P*ye),Math.sin(P*ye)],q=G[0],H=G[1],Q=[q+j*Math.sin(P*ye),H-j*Math.cos(P*ye)],N=Q[0],M=Q[1];T[O]={relative:h.relative,type:m.CURVE_TO};var K=function(se,ge){var Se=yi([se*h.rX,ge*h.rY],h.xRot),ze=Se[0],It=Se[1];return[h.cX+ze,h.cY+It]};v=K(X,W),T[O].x1=v[0],T[O].y1=v[1],g=K(N,M),T[O].x2=g[0],T[O].y2=g[1],d=K(q,H),T[O].x=d[0],T[O].y=d[1],h.relative&&(T[O].x1-=$,T[O].y1-=E,T[O].x2-=$,T[O].y2-=E,T[O].x-=$,T[O].y-=E),$=(p=[T[O].x,T[O].y])[0],E=p[1]}return T}(s,s.relative?0:u,s.relative?0:l):s})},a.ANNOTATE_ARCS=function(){return i(function(s,u,l){return s.relative&&(u=0,l=0),m.ARC===s.type&&Ua(s,u,l),s})},a.CLONE=o,a.CALCULATE_BOUNDS=function(){var s=function(c){var v={};for(var g in c)v[g]=c[g];return v},u=e(),l=r(),h=t(),f=i(function(c,v,g){var d=h(l(u(s(c))));function p(M){M>f.maxX&&(f.maxX=M),M<f.minX&&(f.minX=M)}function y(M){M>f.maxY&&(f.maxY=M),M<f.minY&&(f.minY=M)}if(d.type&m.DRAWING_COMMANDS&&(p(v),y(g)),d.type&m.HORIZ_LINE_TO&&p(d.x),d.type&m.VERT_LINE_TO&&y(d.y),d.type&m.LINE_TO&&(p(d.x),y(d.y)),d.type&m.CURVE_TO){p(d.x),y(d.y);for(var x=0,b=co(v,d.x1,d.x2,d.x);x<b.length;x++)0<(N=b[x])&&1>N&&p(go(v,d.x1,d.x2,d.x,N));for(var T=0,$=co(g,d.y1,d.y2,d.y);T<$.length;T++)0<(N=$[T])&&1>N&&y(go(g,d.y1,d.y2,d.y,N))}if(d.type&m.ARC){p(d.x),y(d.y),Ua(d,v,g);for(var E=d.xRot/180*Math.PI,O=Math.cos(E)*d.rX,C=Math.sin(E)*d.rX,P=-Math.sin(E)*d.rY,V=Math.cos(E)*d.rY,j=d.phi1<d.phi2?[d.phi1,d.phi2]:-180>d.phi2?[d.phi2+360,d.phi1+360]:[d.phi2,d.phi1],R=j[0],X=j[1],W=function(M){var K=M[0],se=M[1],ge=180*Math.atan2(se,K)/Math.PI;return ge<R?ge+360:ge},G=0,q=ho(P,-O,0).map(W);G<q.length;G++)(N=q[G])>R&&N<X&&p(fo(d.cX,O,P,N));for(var H=0,Q=ho(V,-C,0).map(W);H<Q.length;H++){var N;(N=Q[H])>R&&N<X&&y(fo(d.cY,C,V,N))}}return c});return f.minX=1/0,f.maxX=-1/0,f.minY=1/0,f.maxY=-1/0,f}})(U||(U={}));var oe,vl=function(){function a(){}return a.prototype.round=function(e){return this.transform(U.ROUND(e))},a.prototype.toAbs=function(){return this.transform(U.TO_ABS())},a.prototype.toRel=function(){return this.transform(U.TO_REL())},a.prototype.normalizeHVZ=function(e,t,r){return this.transform(U.NORMALIZE_HVZ(e,t,r))},a.prototype.normalizeST=function(){return this.transform(U.NORMALIZE_ST())},a.prototype.qtToC=function(){return this.transform(U.QT_TO_C())},a.prototype.aToC=function(){return this.transform(U.A_TO_C())},a.prototype.sanitize=function(e){return this.transform(U.SANITIZE(e))},a.prototype.translate=function(e,t){return this.transform(U.TRANSLATE(e,t))},a.prototype.scale=function(e,t){return this.transform(U.SCALE(e,t))},a.prototype.rotate=function(e,t,r){return this.transform(U.ROTATE(e,t,r))},a.prototype.matrix=function(e,t,r,i,n,o){return this.transform(U.MATRIX(e,t,r,i,n,o))},a.prototype.skewX=function(e){return this.transform(U.SKEW_X(e))},a.prototype.skewY=function(e){return this.transform(U.SKEW_Y(e))},a.prototype.xSymmetry=function(e){return this.transform(U.X_AXIS_SYMMETRY(e))},a.prototype.ySymmetry=function(e){return this.transform(U.Y_AXIS_SYMMETRY(e))},a.prototype.annotateArcs=function(){return this.transform(U.ANNOTATE_ARCS())},a}(),Fb=function(a){return a===" "||a==="	"||a==="\r"||a===`
`},po=function(a){return 48<=a.charCodeAt(0)&&a.charCodeAt(0)<=57},Ub=function(a){function e(){var t=a.call(this)||this;return t.curNumber="",t.curCommandType=-1,t.curCommandRelative=!1,t.canParseCommandOrComma=!0,t.curNumberHasExp=!1,t.curNumberHasExpDigits=!1,t.curNumberHasDecimal=!1,t.curArgs=[],t}return hl(e,a),e.prototype.finish=function(t){if(t===void 0&&(t=[]),this.parse(" ",t),this.curArgs.length!==0||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,r){var i=this;r===void 0&&(r=[]);for(var n=function(f){r.push(f),i.curArgs.length=0,i.canParseCommandOrComma=!0},o=0;o<t.length;o++){var s=t[o],u=!(this.curCommandType!==m.ARC||this.curArgs.length!==3&&this.curArgs.length!==4||this.curNumber.length!==1||this.curNumber!=="0"&&this.curNumber!=="1"),l=po(s)&&(this.curNumber==="0"&&s==="0"||u);if(!po(s)||l)if(s!=="e"&&s!=="E")if(s!=="-"&&s!=="+"||!this.curNumberHasExp||this.curNumberHasExpDigits)if(s!=="."||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&this.curCommandType!==-1){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+o);if(this.curCommandType===m.ARC){if(this.curArgs.length===0||this.curArgs.length===1){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+o+'"')}else if((this.curArgs.length===3||this.curArgs.length===4)&&this.curNumber!=="0"&&this.curNumber!=="1")throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+o+'"')}this.curArgs.push(h),this.curArgs.length===Gb[this.curCommandType]&&(m.HORIZ_LINE_TO===this.curCommandType?n({type:m.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):m.VERT_LINE_TO===this.curCommandType?n({type:m.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===m.MOVE_TO||this.curCommandType===m.LINE_TO||this.curCommandType===m.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),m.MOVE_TO===this.curCommandType&&(this.curCommandType=m.LINE_TO)):this.curCommandType===m.CURVE_TO?n({type:m.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===m.SMOOTH_CURVE_TO?n({type:m.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===m.QUAD_TO?n({type:m.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===m.ARC&&n({type:m.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!Fb(s))if(s===","&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if(s!=="+"&&s!=="-"&&s!==".")if(l)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(this.curArgs.length!==0)throw new SyntaxError("Unterminated command at index "+o+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,s!=="z"&&s!=="Z")if(s==="h"||s==="H")this.curCommandType=m.HORIZ_LINE_TO,this.curCommandRelative=s==="h";else if(s==="v"||s==="V")this.curCommandType=m.VERT_LINE_TO,this.curCommandRelative=s==="v";else if(s==="m"||s==="M")this.curCommandType=m.MOVE_TO,this.curCommandRelative=s==="m";else if(s==="l"||s==="L")this.curCommandType=m.LINE_TO,this.curCommandRelative=s==="l";else if(s==="c"||s==="C")this.curCommandType=m.CURVE_TO,this.curCommandRelative=s==="c";else if(s==="s"||s==="S")this.curCommandType=m.SMOOTH_CURVE_TO,this.curCommandRelative=s==="s";else if(s==="q"||s==="Q")this.curCommandType=m.QUAD_TO,this.curCommandRelative=s==="q";else if(s==="t"||s==="T")this.curCommandType=m.SMOOTH_QUAD_TO,this.curCommandRelative=s==="t";else{if(s!=="a"&&s!=="A")throw new SyntaxError('Unexpected character "'+s+'" at index '+o+".");this.curCommandType=m.ARC,this.curCommandRelative=s==="a"}else r.push({type:m.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal=s==="."}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return r},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(r,i){i===void 0&&(i=[]);for(var n=0,o=Object.getPrototypeOf(this).parse.call(this,r);n<o.length;n++){var s=o[n],u=t(s);Array.isArray(u)?i.push.apply(i,u):i.push(u)}return i}}})},e}(vl),m=function(a){function e(t){var r=a.call(this)||this;return r.commands=typeof t=="string"?e.parse(t):t,r}return hl(e,a),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=U.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var r=[],i=0,n=this.commands;i<n.length;i++){var o=t(n[i]);Array.isArray(o)?r.push.apply(r,o):r.push(o)}return this.commands=r,this},e.encode=function(t){return jb(t)},e.parse=function(t){var r=new Ub,i=[];return r.parse(t,i),r.finish(i),i},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(vl),Gb=((oe={})[m.MOVE_TO]=2,oe[m.LINE_TO]=2,oe[m.HORIZ_LINE_TO]=1,oe[m.VERT_LINE_TO]=1,oe[m.CLOSE_PATH]=0,oe[m.QUAD_TO]=4,oe[m.SMOOTH_QUAD_TO]=2,oe[m.CURVE_TO]=6,oe[m.SMOOTH_CURVE_TO]=4,oe[m.ARC]=7,oe),zb=$r.PROPER,Hb=Ue,Yb=J,yo=pe,Xb=D,Wb=Wi,tn="toString",fl=RegExp.prototype,cl=fl[tn],qb=Xb(function(){return cl.call({source:"a",flags:"b"})!=="/a/b"}),Qb=zb&&cl.name!==tn;(qb||Qb)&&Hb(fl,tn,function(){var e=Yb(this),t=yo(e.source),r=yo(Wb(e));return"/"+t+"/"+r},{unsafe:!0});function fr(a){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?fr=function(e){return typeof e}:fr=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fr(a)}function Kb(a,e){if(!(a instanceof e))throw new TypeError("Cannot call a class as a function")}var Zb=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],Jb=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function e1(a,e,t,r,i){if(typeof a=="string"&&(a=document.getElementById(a)),!a||fr(a)!=="object"||!("getContext"in a))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var n=a.getContext("2d");try{return n.getImageData(e,t,r,i)}catch(o){throw new Error("unable to access image data: "+o)}}function t1(a,e,t,r,i,n){if(!(isNaN(n)||n<1)){n|=0;var o=e1(a,e,t,r,i);o=r1(o,e,t,r,i,n),a.getContext("2d").putImageData(o,e,t)}}function r1(a,e,t,r,i,n){for(var o=a.data,s=2*n+1,u=r-1,l=i-1,h=n+1,f=h*(h+1)/2,c=new mo,v=c,g,d=1;d<s;d++)v=v.next=new mo,d===h&&(g=v);v.next=c;for(var p=null,y=null,x=0,b=0,T=Zb[n],$=Jb[n],E=0;E<i;E++){v=c;for(var O=o[b],C=o[b+1],P=o[b+2],V=o[b+3],j=0;j<h;j++)v.r=O,v.g=C,v.b=P,v.a=V,v=v.next;for(var R=0,X=0,W=0,G=0,q=h*O,H=h*C,Q=h*P,N=h*V,M=f*O,K=f*C,se=f*P,ge=f*V,Se=1;Se<h;Se++){var ze=b+((u<Se?u:Se)<<2),It=o[ze],nn=o[ze+1],sn=o[ze+2],on=o[ze+3],Mt=h-Se;M+=(v.r=It)*Mt,K+=(v.g=nn)*Mt,se+=(v.b=sn)*Mt,ge+=(v.a=on)*Mt,R+=It,X+=nn,W+=sn,G+=on,v=v.next}p=c,y=g;for(var jr=0;jr<r;jr++){var Fr=ge*T>>>$;if(o[b+3]=Fr,Fr!==0){var Ur=255/Fr;o[b]=(M*T>>>$)*Ur,o[b+1]=(K*T>>>$)*Ur,o[b+2]=(se*T>>>$)*Ur}else o[b]=o[b+1]=o[b+2]=0;M-=q,K-=H,se-=Q,ge-=N,q-=p.r,H-=p.g,Q-=p.b,N-=p.a;var Ae=jr+n+1;Ae=x+(Ae<u?Ae:u)<<2,R+=p.r=o[Ae],X+=p.g=o[Ae+1],W+=p.b=o[Ae+2],G+=p.a=o[Ae+3],M+=R,K+=X,se+=W,ge+=G,p=p.next;var _t=y,un=_t.r,ln=_t.g,hn=_t.b,vn=_t.a;q+=un,H+=ln,Q+=hn,N+=vn,R-=un,X-=ln,W-=hn,G-=vn,y=y.next,b+=4}x+=r}for(var He=0;He<r;He++){b=He<<2;var Pe=o[b],Re=o[b+1],Ne=o[b+2],te=o[b+3],Gr=h*Pe,zr=h*Re,Hr=h*Ne,Yr=h*te,Vt=f*Pe,Dt=f*Re,Lt=f*Ne,kt=f*te;v=c;for(var fn=0;fn<h;fn++)v.r=Pe,v.g=Re,v.b=Ne,v.a=te,v=v.next;for(var cn=r,Xr=0,Wr=0,qr=0,Qr=0,Bt=1;Bt<=n;Bt++){b=cn+He<<2;var jt=h-Bt;Vt+=(v.r=Pe=o[b])*jt,Dt+=(v.g=Re=o[b+1])*jt,Lt+=(v.b=Ne=o[b+2])*jt,kt+=(v.a=te=o[b+3])*jt,Qr+=Pe,Xr+=Re,Wr+=Ne,qr+=te,v=v.next,Bt<l&&(cn+=r)}b=He,p=c,y=g;for(var Kr=0;Kr<i;Kr++){var re=b<<2;o[re+3]=te=kt*T>>>$,te>0?(te=255/te,o[re]=(Vt*T>>>$)*te,o[re+1]=(Dt*T>>>$)*te,o[re+2]=(Lt*T>>>$)*te):o[re]=o[re+1]=o[re+2]=0,Vt-=Gr,Dt-=zr,Lt-=Hr,kt-=Yr,Gr-=p.r,zr-=p.g,Hr-=p.b,Yr-=p.a,re=He+((re=Kr+h)<l?re:l)*r<<2,Vt+=Qr+=p.r=o[re],Dt+=Xr+=p.g=o[re+1],Lt+=Wr+=p.b=o[re+2],kt+=qr+=p.a=o[re+3],p=p.next,Gr+=Pe=y.r,zr+=Re=y.g,Hr+=Ne=y.b,Yr+=te=y.a,Qr-=Pe,Xr-=Re,Wr-=Ne,qr-=te,y=y.next,b+=r}}return a}var mo=function a(){Kb(this,a),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function a1(){var{DOMParser:a}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:a,createCanvas(t,r){return new OffscreenCanvas(t,r)},createImage(t){return xe(function*(){var r=yield fetch(t),i=yield r.blob(),n=yield createImageBitmap(i);return n})()}};return(typeof DOMParser<"u"||typeof a>"u")&&Reflect.deleteProperty(e,"DOMParser"),e}function i1(a){var{DOMParser:e,canvas:t,fetch:r}=a;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:r,createCanvas:t.createCanvas,createImage:t.loadImage}}var p2=Object.freeze({__proto__:null,offscreen:a1,node:i1});function lt(a){return a.replace(/(?!\u3000)\s+/gm," ")}function n1(a){return a.replace(/^[\n \t]+/,"")}function s1(a){return a.replace(/[\n \t]+$/,"")}function ne(a){var e=(a||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[];return e.map(parseFloat)}var o1=/^[A-Z-]+$/;function u1(a){return o1.test(a)?a.toLowerCase():a}function gl(a){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(a)||[];return e[2]||e[3]||e[4]}function l1(a){if(!a.startsWith("rgb"))return a;var e=3,t=a.replace(/\d+(\.\d+)?/g,(r,i)=>e--&&i?String(Math.round(parseFloat(r))):r);return t}var h1=/(\[[^\]]+\])/g,v1=/(#[^\s+>~.[:]+)/g,f1=/(\.[^\s+>~.[:]+)/g,c1=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,g1=/(:[\w-]+\([^)]*\))/gi,d1=/(:[^\s+>~.[:]+)/g,p1=/([^\s+>~.[:]+)/g;function Ve(a,e){var t=e.exec(a);return t?[a.replace(e," "),t.length]:[a,0]}function y1(a){var e=[0,0,0],t=a.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[t,r]=Ve(t,h1),e[1]+=r,[t,r]=Ve(t,v1),e[0]+=r,[t,r]=Ve(t,f1),e[1]+=r,[t,r]=Ve(t,c1),e[2]+=r,[t,r]=Ve(t,g1),e[1]+=r,[t,r]=Ve(t,d1),e[1]+=r,t=t.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[t,r]=Ve(t,p1),e[2]+=r,e.join("")}var rt=1e-8;function bo(a){return Math.sqrt(Math.pow(a[0],2)+Math.pow(a[1],2))}function mi(a,e){return(a[0]*e[0]+a[1]*e[1])/(bo(a)*bo(e))}function xo(a,e){return(a[0]*e[1]<a[1]*e[0]?-1:1)*Math.acos(mi(a,e))}function To(a){return a*a*a}function Oo(a){return 3*a*a*(1-a)}function So(a){return 3*a*(1-a)*(1-a)}function Eo(a){return(1-a)*(1-a)*(1-a)}function $o(a){return a*a}function wo(a){return 2*a*(1-a)}function Co(a){return(1-a)*(1-a)}class S{constructor(e,t,r){this.document=e,this.name=t,this.value=r,this.isNormalizedColor=!1}static empty(e){return new S(e,"EMPTY","")}split(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:" ",{document:t,name:r}=this;return lt(this.getString()).trim().split(e).map(i=>new S(t,r,i))}hasValue(e){var{value:t}=this;return t!==null&&t!==""&&(e||t!==0)&&typeof t<"u"}isString(e){var{value:t}=this,r=typeof t=="string";return!r||!e?r:e.test(t)}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var e=this.getString();switch(!0){case e.endsWith("px"):case/^[0-9]+$/.test(e):return!0;default:return!1}}setValue(e){return this.value=e,this}getValue(e){return typeof e>"u"||this.hasValue()?this.value:e}getNumber(e){if(!this.hasValue())return typeof e>"u"?0:parseFloat(e);var{value:t}=this,r=parseFloat(t);return this.isString(/%$/)&&(r/=100),r}getString(e){return typeof e>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(e)}getColor(e){var t=this.getString(e);return this.isNormalizedColor||(this.isNormalizedColor=!0,t=l1(t),this.value=t),t}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!this.hasValue())return 0;var[r,i]=typeof e=="boolean"?[void 0,e]:[e],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return this.getNumber()*15;case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case(this.isString(/%$/)&&i):return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:{var o=this.getNumber();return t&&o<1?o*n.computeSize(r):o}}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():this.getNumber()*1e3:0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var e=this.getString(),t=/#([^)'"]+)/.exec(e);return t&&(t=t[1]),t||(t=e),this.document.definitions[t]}getFillStyleDefinition(e,t){var r=this.getDefinition();if(!r)return null;if(typeof r.createGradient=="function")return r.createGradient(this.document.ctx,e,t);if(typeof r.createPattern=="function"){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,e,t)}return null}getTextBaseline(){return this.hasValue()?S.textBaselineMapping[this.getString()]:null}addOpacity(e){for(var t=this.getColor(),r=t.length,i=0,n=0;n<r&&(t[n]===","&&i++,i!==3);n++);if(e.hasValue()&&this.isString()&&i!==3){var o=new di(t);o.ok&&(o.alpha=e.getNumber(),t=o.toRGBA())}return new S(this.document,this.name,t)}}S.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class m1{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(e,t){this.viewPorts.push({width:e,height:t})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:e}=this;return e[e.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(e){return typeof e=="number"?e:e==="x"?this.width:e==="y"?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class k{constructor(e,t){this.x=e,this.y=t}static parse(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,[r=t,i=t]=ne(e);return new k(r,i)}static parseScale(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,[r=t,i=r]=ne(e);return new k(r,i)}static parsePath(e){for(var t=ne(e),r=t.length,i=[],n=0;n<r;n+=2)i.push(new k(t[n],t[n+1]));return i}angleTo(e){return Math.atan2(e.y-this.y,e.x-this.x)}applyTransform(e){var{x:t,y:r}=this,i=t*e[0]+r*e[2]+e[4],n=t*e[1]+r*e[3]+e[5];this.x=i,this.y=n}}class b1{constructor(e){this.screen=e,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:e,onClick:t,onMouseMove:r}=this,i=e.ctx.canvas;i.onclick=t,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var e=this.screen.ctx.canvas;this.working=!1,e.onclick=null,e.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:e,events:t,eventElements:r}=this,{style:i}=e.ctx.canvas;i&&(i.cursor=""),t.forEach((n,o)=>{for(var{run:s}=n,u=r[o];u;)s(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(e,t){if(!(!this.working||!t)){var{events:r,eventElements:i}=this;r.forEach((n,o)=>{var{x:s,y:u}=n;!i[o]&&t.isPointInPath&&t.isPointInPath(s,u)&&(i[o]=e)})}}checkBoundingBox(e,t){if(!(!this.working||!t)){var{events:r,eventElements:i}=this;r.forEach((n,o)=>{var{x:s,y:u}=n;!i[o]&&t.isPointInBox(s,u)&&(i[o]=e)})}}mapXY(e,t){for(var{window:r,ctx:i}=this.screen,n=new k(e,t),o=i.canvas;o;)n.x-=o.offsetLeft,n.y-=o.offsetTop,o=o.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(e){var{x:t,y:r}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onclick",x:t,y:r,run(i){i.onClick&&i.onClick()}})}onMouseMove(e){var{x:t,y:r}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onmousemove",x:t,y:r,run(i){i.onMouseMove&&i.onMouseMove()}})}}var dl=typeof window<"u"?window:null,pl=typeof fetch<"u"?fetch.bind(void 0):null;class Dr{constructor(e){var{fetch:t=pl,window:r=dl}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new m1,this.mouse=new b1(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=t}wait(e){this.waits.push(e)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var e=this.waits.every(t=>t());return e&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=e,e}setDefaults(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4}setViewBox(e){var{document:t,ctx:r,aspectRatio:i,width:n,desiredWidth:o,height:s,desiredHeight:u,minX:l=0,minY:h=0,refX:f,refY:c,clip:v=!1,clipX:g=0,clipY:d=0}=e,p=lt(i).replace(/^defer\s/,""),[y,x]=p.split(" "),b=y||"xMidYMid",T=x||"meet",$=n/o,E=s/u,O=Math.min($,E),C=Math.max($,E),P=o,V=u;T==="meet"&&(P*=O,V*=O),T==="slice"&&(P*=C,V*=C);var j=new S(t,"refX",f),R=new S(t,"refY",c),X=j.hasValue()&&R.hasValue();if(X&&r.translate(-O*j.getPixels("x"),-O*R.getPixels("y")),v){var W=O*g,G=O*d;r.beginPath(),r.moveTo(W,G),r.lineTo(n,G),r.lineTo(n,s),r.lineTo(W,s),r.closePath(),r.clip()}if(!X){var q=T==="meet"&&O===E,H=T==="slice"&&C===E,Q=T==="meet"&&O===$,N=T==="slice"&&C===$;b.startsWith("xMid")&&(q||H)&&r.translate(n/2-P/2,0),b.endsWith("YMid")&&(Q||N)&&r.translate(0,s/2-V/2),b.startsWith("xMax")&&(q||H)&&r.translate(n-P,0),b.endsWith("YMax")&&(Q||N)&&r.translate(0,s-V)}switch(!0){case b==="none":r.scale($,E);break;case T==="meet":r.scale(O,O);break;case T==="slice":r.scale(C,C);break}r.translate(-l,-h)}start(e){var{enableRedraw:t=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:o=!1,forceRedraw:s,scaleWidth:u,scaleHeight:l,offsetX:h,offsetY:f}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{FRAMERATE:c,mouse:v}=this,g=1e3/c;if(this.frameDuration=g,this.readyPromise=new Promise(b=>{this.resolveReady=b}),this.isReady()&&this.render(e,n,o,u,l,h,f),!!t){var d=Date.now(),p=d,y=0,x=()=>{d=Date.now(),y=d-p,y>=g&&(p=d-y%g,this.shouldUpdate(i,s)&&(this.render(e,n,o,u,l,h,f),v.runEvents())),this.intervalId=Fa(x)};r||v.start(),this.intervalId=Fa(x)}}stop(){this.intervalId&&(Fa.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(e,t){if(!e){var{frameDuration:r}=this,i=this.animations.reduce((n,o)=>o.update(r)||n,!1);if(i)return!0}return!!(typeof t=="function"&&t()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(e,t,r,i,n,o,s){var{CLIENT_WIDTH:u,CLIENT_HEIGHT:l,viewPort:h,ctx:f,isFirstRender:c}=this,v=f.canvas;h.clear(),v.width&&v.height?h.setCurrent(v.width,v.height):h.setCurrent(u,l);var g=e.getStyle("width"),d=e.getStyle("height");!t&&(c||typeof i!="number"&&typeof n!="number")&&(g.hasValue()&&(v.width=g.getPixels("x"),v.style&&(v.style.width="".concat(v.width,"px"))),d.hasValue()&&(v.height=d.getPixels("y"),v.style&&(v.style.height="".concat(v.height,"px"))));var p=v.clientWidth||v.width,y=v.clientHeight||v.height;if(t&&g.hasValue()&&d.hasValue()&&(p=g.getPixels("x"),y=d.getPixels("y")),h.setCurrent(p,y),typeof o=="number"&&e.getAttribute("x",!0).setValue(o),typeof s=="number"&&e.getAttribute("y",!0).setValue(s),typeof i=="number"||typeof n=="number"){var x=ne(e.getAttribute("viewBox").getString()),b=0,T=0;if(typeof i=="number"){var $=e.getStyle("width");$.hasValue()?b=$.getPixels("x")/i:isNaN(x[2])||(b=x[2]/i)}if(typeof n=="number"){var E=e.getStyle("height");E.hasValue()?T=E.getPixels("y")/n:isNaN(x[3])||(T=x[3]/n)}b||(b=T),T||(T=b),e.getAttribute("width",!0).setValue(i),e.getAttribute("height",!0).setValue(n);var O=e.getStyle("transform",!0,!0);O.setValue("".concat(O.getString()," scale(").concat(1/b,", ").concat(1/T,")"))}r||f.clearRect(0,0,p,y),e.render(f),c&&(this.isFirstRender=!1)}}Dr.defaultWindow=dl;Dr.defaultFetch=pl;var{defaultFetch:x1}=Dr,T1=typeof DOMParser<"u"?DOMParser:null;class Ga{constructor(){var{fetch:e=x1,DOMParser:t=T1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fetch=e,this.DOMParser=t}parse(e){var t=this;return xe(function*(){return e.startsWith("<")?t.parseFromString(e):t.load(e)})()}parseFromString(e){var t=new this.DOMParser;try{return this.checkDocument(t.parseFromString(e,"image/svg+xml"))}catch{return this.checkDocument(t.parseFromString(e,"text/xml"))}}checkDocument(e){var t=e.getElementsByTagName("parsererror")[0];if(t)throw new Error(t.textContent);return e}load(e){var t=this;return xe(function*(){var r=yield t.fetch(e),i=yield r.text();return t.parseFromString(i)})()}}class O1{constructor(e,t){this.type="translate",this.point=null,this.point=k.parse(t)}apply(e){var{x:t,y:r}=this.point;e.translate(t||0,r||0)}unapply(e){var{x:t,y:r}=this.point;e.translate(-1*t||0,-1*r||0)}applyToPoint(e){var{x:t,y:r}=this.point;e.applyTransform([1,0,0,1,t||0,r||0])}}class S1{constructor(e,t,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=ne(t);this.angle=new S(e,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(e){var{cx:t,cy:r,originX:i,originY:n,angle:o}=this,s=t+i.getPixels("x"),u=r+n.getPixels("y");e.translate(s,u),e.rotate(o.getRadians()),e.translate(-s,-u)}unapply(e){var{cx:t,cy:r,originX:i,originY:n,angle:o}=this,s=t+i.getPixels("x"),u=r+n.getPixels("y");e.translate(s,u),e.rotate(-1*o.getRadians()),e.translate(-s,-u)}applyToPoint(e){var{cx:t,cy:r,angle:i}=this,n=i.getRadians();e.applyTransform([1,0,0,1,t||0,r||0]),e.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),e.applyTransform([1,0,0,1,-t||0,-r||0])}}class E1{constructor(e,t,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=k.parseScale(t);(i.x===0||i.y===0)&&(i.x=rt,i.y=rt),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(e){var{scale:{x:t,y:r},originX:i,originY:n}=this,o=i.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(t,r||t),e.translate(-o,-s)}unapply(e){var{scale:{x:t,y:r},originX:i,originY:n}=this,o=i.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(1/t,1/r||t),e.translate(-o,-s)}applyToPoint(e){var{x:t,y:r}=this.scale;e.applyTransform([t||0,0,0,r||0,0,0])}}class yl{constructor(e,t,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=ne(t),this.originX=r[0],this.originY=r[1]}apply(e){var{originX:t,originY:r,matrix:i}=this,n=t.getPixels("x"),o=r.getPixels("y");e.translate(n,o),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),e.translate(-n,-o)}unapply(e){var{originX:t,originY:r,matrix:i}=this,n=i[0],o=i[2],s=i[4],u=i[1],l=i[3],h=i[5],f=0,c=0,v=1,g=1/(n*(l*v-h*c)-o*(u*v-h*f)+s*(u*c-l*f)),d=t.getPixels("x"),p=r.getPixels("y");e.translate(d,p),e.transform(g*(l*v-h*c),g*(h*f-u*v),g*(s*c-o*v),g*(n*v-s*f),g*(o*h-s*l),g*(s*u-n*h)),e.translate(-d,-p)}applyToPoint(e){e.applyTransform(this.matrix)}}class ml extends yl{constructor(e,t,r){super(e,t,r),this.type="skew",this.angle=null,this.angle=new S(e,"angle",t)}}class $1 extends ml{constructor(e,t,r){super(e,t,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class w1 extends ml{constructor(e,t,r){super(e,t,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}function C1(a){return lt(a).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}function A1(a){var[e,t]=a.split("(");return[e.trim(),t.trim().replace(")","")]}class Be{constructor(e,t,r){this.document=e,this.transforms=[];var i=C1(t);i.forEach(n=>{if(n!=="none"){var[o,s]=A1(n),u=Be.transformTypes[o];typeof u<"u"&&this.transforms.push(new u(this.document,s,r))}})}static fromElement(e,t){var r=t.getStyle("transform",!1,!0),[i,n=i]=t.getStyle("transform-origin",!1,!0).split(),o=[i,n];return r.hasValue()?new Be(e,r.getString(),o):null}apply(e){for(var{transforms:t}=this,r=t.length,i=0;i<r;i++)t[i].apply(e)}unapply(e){for(var{transforms:t}=this,r=t.length,i=r-1;i>=0;i--)t[i].unapply(e)}applyToPoint(e){for(var{transforms:t}=this,r=t.length,i=0;i<r;i++)t[i].applyToPoint(e)}}Be.transformTypes={translate:O1,rotate:S1,scale:E1,matrix:yl,skewX:$1,skewY:w1};class I{constructor(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.document=e,this.node=t,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!(!t||t.nodeType!==1)){if(Array.from(t.attributes).forEach(s=>{var u=u1(s.nodeName);this.attributes[u]=new S(e,u,s.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var i=this.getAttribute("style").getString().split(";").map(s=>s.trim());i.forEach(s=>{if(s){var[u,l]=s.split(":").map(h=>h.trim());this.styles[u]=new S(e,u,l)}})}var{definitions:n}=e,o=this.getAttribute("id");o.hasValue()&&(n[o.getString()]||(n[o.getString()]=this)),Array.from(t.childNodes).forEach(s=>{if(s.nodeType===1)this.addChild(s);else if(r&&(s.nodeType===3||s.nodeType===4)){var u=e.createTextNode(s);u.getText().length>0&&this.addChild(u)}})}}getAttribute(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=this.attributes[e];if(!r&&t){var i=new S(this.document,e,"");return this.attributes[e]=i,i}return r||S.empty(this.document)}getHrefAttribute(){for(var e in this.attributes)if(e==="href"||e.endsWith(":href"))return this.attributes[e];return S.empty(this.document)}getStyle(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.styles[e];if(i)return i;var n=this.getAttribute(e);if(n!=null&&n.hasValue())return this.styles[e]=n,n;if(!r){var{parent:o}=this;if(o){var s=o.getStyle(e);if(s!=null&&s.hasValue())return s}}if(t){var u=new S(this.document,e,"");return this.styles[e]=u,u}return i||S.empty(this.document)}render(e){if(!(this.getStyle("display").getString()==="none"||this.getStyle("visibility").getString()==="hidden")){if(e.save(),this.getStyle("mask").hasValue()){var t=this.getStyle("mask").getDefinition();t&&(this.applyEffects(e),t.apply(e,this))}else if(this.getStyle("filter").getValue("none")!=="none"){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(e),r.apply(e,this))}else this.setContext(e),this.renderChildren(e),this.clearContext(e);e.restore()}}setContext(e){}applyEffects(e){var t=Be.fromElement(this.document,this);t&&t.apply(e);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(e)}}clearContext(e){}renderChildren(e){this.children.forEach(t=>{t.render(e)})}addChild(e){var t=e instanceof I?e:this.document.createElement(e);t.parent=this,I.ignoreChildTypes.includes(t.type)||this.children.push(t)}matchesSelector(e){var t,{node:r}=this;if(typeof r.matches=="function")return r.matches(e);var i=(t=r.getAttribute)===null||t===void 0?void 0:t.call(r,"class");return!i||i===""?!1:i.split(" ").some(n=>".".concat(n)===e)}addStylesFromStyleDefinition(){var{styles:e,stylesSpecificity:t}=this.document;for(var r in e)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=e[r],n=t[r];if(i)for(var o in i){var s=this.stylesSpecificity[o];typeof s>"u"&&(s="000"),n>=s&&(this.styles[o]=i[o],this.stylesSpecificity[o]=n)}}}removeStyles(e,t){var r=t.reduce((i,n)=>{var o=e.getStyle(n);if(!o.hasValue())return i;var s=o.getString();return o.setValue(""),[...i,[n,s]]},[]);return r}restoreStyles(e,t){t.forEach(r=>{var[i,n]=r;e.getStyle(i,!0).setValue(n)})}isFirstChild(){var e;return((e=this.parent)===null||e===void 0?void 0:e.children.indexOf(this))===0}}I.ignoreChildTypes=["title"];class P1 extends I{constructor(e,t,r){super(e,t,r)}}function R1(a){var e=a.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function N1(a){return typeof process>"u"?a:a.trim().split(",").map(R1).join(",")}function I1(a){if(!a)return"";var e=a.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function M1(a){if(!a)return"";var e=a.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class Z{constructor(e,t,r,i,n,o){var s=o?typeof o=="string"?Z.parse(o):o:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=e||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=t||s.fontVariant}static parse(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,r="",i="",n="",o="",s="",u=lt(e).trim().split(" "),l={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return u.forEach(h=>{switch(!0){case(!l.fontStyle&&Z.styles.includes(h)):h!=="inherit"&&(r=h),l.fontStyle=!0;break;case(!l.fontVariant&&Z.variants.includes(h)):h!=="inherit"&&(i=h),l.fontStyle=!0,l.fontVariant=!0;break;case(!l.fontWeight&&Z.weights.includes(h)):h!=="inherit"&&(n=h),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0;break;case!l.fontSize:h!=="inherit"&&([o]=h.split("/")),l.fontStyle=!0,l.fontVariant=!0,l.fontWeight=!0,l.fontSize=!0;break;default:h!=="inherit"&&(s+=h)}}),new Z(r,i,n,o,s,t)}toString(){return[I1(this.fontStyle),this.fontVariant,M1(this.fontWeight),this.fontSize,N1(this.fontFamily)].join(" ").trim()}}Z.styles="normal|italic|oblique|inherit";Z.variants="normal|small-caps|inherit";Z.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class ce{constructor(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Number.NaN,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.NaN,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Number.NaN,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Number.NaN;this.x1=e,this.y1=t,this.x2=r,this.y2=i,this.addPoint(e,t),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(e,t){typeof e<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof t<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=t,this.y2=t),t<this.y1&&(this.y1=t),t>this.y2&&(this.y2=t))}addX(e){this.addPoint(e,null)}addY(e){this.addPoint(null,e)}addBoundingBox(e){if(e){var{x1:t,y1:r,x2:i,y2:n}=e;this.addPoint(t,r),this.addPoint(i,n)}}sumCubic(e,t,r,i,n){return Math.pow(1-e,3)*t+3*Math.pow(1-e,2)*e*r+3*(1-e)*Math.pow(e,2)*i+Math.pow(e,3)*n}bezierCurveAdd(e,t,r,i,n){var o=6*t-12*r+6*i,s=-3*t+9*r-9*i+3*n,u=3*r-3*t;if(s===0){if(o===0)return;var l=-u/o;0<l&&l<1&&(e?this.addX(this.sumCubic(l,t,r,i,n)):this.addY(this.sumCubic(l,t,r,i,n)));return}var h=Math.pow(o,2)-4*u*s;if(!(h<0)){var f=(-o+Math.sqrt(h))/(2*s);0<f&&f<1&&(e?this.addX(this.sumCubic(f,t,r,i,n)):this.addY(this.sumCubic(f,t,r,i,n)));var c=(-o-Math.sqrt(h))/(2*s);0<c&&c<1&&(e?this.addX(this.sumCubic(c,t,r,i,n)):this.addY(this.sumCubic(c,t,r,i,n)))}}addBezierCurve(e,t,r,i,n,o,s,u){this.addPoint(e,t),this.addPoint(s,u),this.bezierCurveAdd(!0,e,r,n,s),this.bezierCurveAdd(!1,t,i,o,u)}addQuadraticCurve(e,t,r,i,n,o){var s=e+.6666666666666666*(r-e),u=t+2/3*(i-t),l=s+1/3*(n-e),h=u+1/3*(o-t);this.addBezierCurve(e,t,s,l,u,h,n,o)}isPointInBox(e,t){var{x1:r,y1:i,x2:n,y2:o}=this;return r<=e&&e<=n&&i<=t&&t<=o}}class w extends m{constructor(e){super(e.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new k(0,0),this.control=new k(0,0),this.current=new k(0,0),this.points=[],this.angles=[]}isEnd(){var{i:e,commands:t}=this;return e>=t.length-1}next(){var e=this.commands[++this.i];return this.previousCommand=this.command,this.command=e,e}getPoint(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"x",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"y",r=new k(this.command[e],this.command[t]);return this.makeAbsolute(r)}getAsControlPoint(e,t){var r=this.getPoint(e,t);return this.control=r,r}getAsCurrentPoint(e,t){var r=this.getPoint(e,t);return this.current=r,r}getReflectedControlPoint(){var e=this.previousCommand.type;if(e!==m.CURVE_TO&&e!==m.SMOOTH_CURVE_TO&&e!==m.QUAD_TO&&e!==m.SMOOTH_QUAD_TO)return this.current;var{current:{x:t,y:r},control:{x:i,y:n}}=this,o=new k(2*t-i,2*r-n);return o}makeAbsolute(e){if(this.command.relative){var{x:t,y:r}=this.current;e.x+=t,e.y+=r}return e}addMarker(e,t,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(e,t?t.angleTo(e):null)}addMarkerAngle(e,t){this.points.push(e),this.angles.push(t)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:e}=this,t=e.length,r=0;r<t;r++)if(!e[r]){for(var i=r+1;i<t;i++)if(e[i]){e[r]=e[i];break}}return e}}class Ge extends I{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var e=1,t=this;t;){var r=t.getStyle("opacity",!1,!0);r.hasValue(!0)&&(e*=r.getNumber()),t=t.parent}return e}setContext(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!t){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(e.fillStyle=s)}else if(r.hasValue()){r.getString()==="currentColor"&&r.setValue(this.getStyle("color").getColor());var u=r.getColor();u!=="inherit"&&(e.fillStyle=u==="none"?"rgba(0,0,0,0)":u)}if(i.hasValue()){var l=new S(this.document,"fill",e.fillStyle).addOpacity(i).getColor();e.fillStyle=l}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,o);h&&(e.strokeStyle=h)}else if(n.hasValue()){n.getString()==="currentColor"&&n.setValue(this.getStyle("color").getColor());var f=n.getString();f!=="inherit"&&(e.strokeStyle=f==="none"?"rgba(0,0,0,0)":f)}if(o.hasValue()){var c=new S(this.document,"stroke",e.strokeStyle).addOpacity(o).getString();e.strokeStyle=c}var v=this.getStyle("stroke-width");if(v.hasValue()){var g=v.getPixels();e.lineWidth=g||rt}var d=this.getStyle("stroke-linecap"),p=this.getStyle("stroke-linejoin"),y=this.getStyle("stroke-miterlimit"),x=this.getStyle("stroke-dasharray"),b=this.getStyle("stroke-dashoffset");if(d.hasValue()&&(e.lineCap=d.getString()),p.hasValue()&&(e.lineJoin=p.getString()),y.hasValue()&&(e.miterLimit=y.getNumber()),x.hasValue()&&x.getString()!=="none"){var T=ne(x.getString());typeof e.setLineDash<"u"?e.setLineDash(T):typeof e.webkitLineDash<"u"?e.webkitLineDash=T:typeof e.mozDash<"u"&&!(T.length===1&&T[0]===0)&&(e.mozDash=T);var $=b.getPixels();typeof e.lineDashOffset<"u"?e.lineDashOffset=$:typeof e.webkitLineDashOffset<"u"?e.webkitLineDashOffset=$:typeof e.mozDashOffset<"u"&&(e.mozDashOffset=$)}}if(this.modifiedEmSizeStack=!1,typeof e.font<"u"){var E=this.getStyle("font"),O=this.getStyle("font-style"),C=this.getStyle("font-variant"),P=this.getStyle("font-weight"),V=this.getStyle("font-size"),j=this.getStyle("font-family"),R=new Z(O.getString(),C.getString(),P.getString(),V.hasValue()?"".concat(V.getPixels(!0),"px"):"",j.getString(),Z.parse(E.getString(),e.font));O.setValue(R.fontStyle),C.setValue(R.fontVariant),P.setValue(R.fontWeight),V.setValue(R.fontSize),j.setValue(R.fontFamily),e.font=R.toString(),V.isPixels()&&(this.document.emSize=V.getPixels(),this.modifiedEmSizeStack=!0)}t||(this.applyEffects(e),e.globalAlpha=this.calculateOpacity())}clearContext(e){super.clearContext(e),this.modifiedEmSizeStack&&this.document.popEmSize()}}class A extends Ge{constructor(e,t,r){super(e,t,r),this.type="path",this.pathParser=null,this.pathParser=new w(this.getAttribute("d").getString())}path(e){var{pathParser:t}=this,r=new ce;for(t.reset(),e&&e.beginPath();!t.isEnd();)switch(t.next().type){case w.MOVE_TO:this.pathM(e,r);break;case w.LINE_TO:this.pathL(e,r);break;case w.HORIZ_LINE_TO:this.pathH(e,r);break;case w.VERT_LINE_TO:this.pathV(e,r);break;case w.CURVE_TO:this.pathC(e,r);break;case w.SMOOTH_CURVE_TO:this.pathS(e,r);break;case w.QUAD_TO:this.pathQ(e,r);break;case w.SMOOTH_QUAD_TO:this.pathT(e,r);break;case w.ARC:this.pathA(e,r);break;case w.CLOSE_PATH:this.pathZ(e,r);break}return r}getBoundingBox(e){return this.path()}getMarkers(){var{pathParser:e}=this,t=e.getMarkerPoints(),r=e.getMarkerAngles(),i=t.map((n,o)=>[n,r[o]]);return i}renderChildren(e){this.path(e),this.document.screen.mouse.checkPath(this,e);var t=this.getStyle("fill-rule");e.fillStyle!==""&&(t.getString("inherit")!=="inherit"?e.fill(t.getString()):e.fill()),e.strokeStyle!==""&&(this.getAttribute("vector-effect").getString()==="non-scaling-stroke"?(e.save(),e.setTransform(1,0,0,1,0,0),e.stroke(),e.restore()):e.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var u=n.getDefinition(),[l,h]=r[0];u.render(e,l,h)}if(o.isUrlDefinition())for(var f=o.getDefinition(),c=1;c<i;c++){var[v,g]=r[c];f.render(e,v,g)}if(s.isUrlDefinition()){var d=s.getDefinition(),[p,y]=r[i];d.render(e,p,y)}}}static pathM(e){var t=e.getAsCurrentPoint();return e.start=e.current,{point:t}}pathM(e,t){var{pathParser:r}=this,{point:i}=A.pathM(r),{x:n,y:o}=i;r.addMarker(i),t.addPoint(n,o),e&&e.moveTo(n,o)}static pathL(e){var{current:t}=e,r=e.getAsCurrentPoint();return{current:t,point:r}}pathL(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathL(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathH(e){var{current:t,command:r}=e,i=new k((r.relative?t.x:0)+r.x,t.y);return e.current=i,{current:t,point:i}}pathH(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathH(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathV(e){var{current:t,command:r}=e,i=new k(t.x,(r.relative?t.y:0)+r.y);return e.current=i,{current:t,point:i}}pathV(e,t){var{pathParser:r}=this,{current:i,point:n}=A.pathV(r),{x:o,y:s}=n;r.addMarker(n,i),t.addPoint(o,s),e&&e.lineTo(o,s)}static pathC(e){var{current:t}=e,r=e.getPoint("x1","y1"),i=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:t,point:r,controlPoint:i,currentPoint:n}}pathC(e,t){var{pathParser:r}=this,{current:i,point:n,controlPoint:o,currentPoint:s}=A.pathC(r);r.addMarker(s,o,n),t.addBezierCurve(i.x,i.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathS(e){var{current:t}=e,r=e.getReflectedControlPoint(),i=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:t,point:r,controlPoint:i,currentPoint:n}}pathS(e,t){var{pathParser:r}=this,{current:i,point:n,controlPoint:o,currentPoint:s}=A.pathS(r);r.addMarker(s,o,n),t.addBezierCurve(i.x,i.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathQ(e){var{current:t}=e,r=e.getAsControlPoint("x1","y1"),i=e.getAsCurrentPoint();return{current:t,controlPoint:r,currentPoint:i}}pathQ(e,t){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:o}=A.pathQ(r);r.addMarker(o,n,n),t.addQuadraticCurve(i.x,i.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathT(e){var{current:t}=e,r=e.getReflectedControlPoint();e.control=r;var i=e.getAsCurrentPoint();return{current:t,controlPoint:r,currentPoint:i}}pathT(e,t){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:o}=A.pathT(r);r.addMarker(o,n,n),t.addQuadraticCurve(i.x,i.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathA(e){var{current:t,command:r}=e,{rX:i,rY:n,xRot:o,lArcFlag:s,sweepFlag:u}=r,l=o*(Math.PI/180),h=e.getAsCurrentPoint(),f=new k(Math.cos(l)*(t.x-h.x)/2+Math.sin(l)*(t.y-h.y)/2,-Math.sin(l)*(t.x-h.x)/2+Math.cos(l)*(t.y-h.y)/2),c=Math.pow(f.x,2)/Math.pow(i,2)+Math.pow(f.y,2)/Math.pow(n,2);c>1&&(i*=Math.sqrt(c),n*=Math.sqrt(c));var v=(s===u?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(f.y,2)-Math.pow(n,2)*Math.pow(f.x,2))/(Math.pow(i,2)*Math.pow(f.y,2)+Math.pow(n,2)*Math.pow(f.x,2)));isNaN(v)&&(v=0);var g=new k(v*i*f.y/n,v*-n*f.x/i),d=new k((t.x+h.x)/2+Math.cos(l)*g.x-Math.sin(l)*g.y,(t.y+h.y)/2+Math.sin(l)*g.x+Math.cos(l)*g.y),p=xo([1,0],[(f.x-g.x)/i,(f.y-g.y)/n]),y=[(f.x-g.x)/i,(f.y-g.y)/n],x=[(-f.x-g.x)/i,(-f.y-g.y)/n],b=xo(y,x);return mi(y,x)<=-1&&(b=Math.PI),mi(y,x)>=1&&(b=0),{currentPoint:h,rX:i,rY:n,sweepFlag:u,xAxisRotation:l,centp:d,a1:p,ad:b}}pathA(e,t){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:o,sweepFlag:s,xAxisRotation:u,centp:l,a1:h,ad:f}=A.pathA(r),c=1-s?1:-1,v=h+c*(f/2),g=new k(l.x+n*Math.cos(v),l.y+o*Math.sin(v));if(r.addMarkerAngle(g,v-c*Math.PI/2),r.addMarkerAngle(i,v-c*Math.PI),t.addPoint(i.x,i.y),e&&!isNaN(h)&&!isNaN(f)){var d=n>o?n:o,p=n>o?1:n/o,y=n>o?o/n:1;e.translate(l.x,l.y),e.rotate(u),e.scale(p,y),e.arc(0,0,d,h,h+f,!!(1-s)),e.scale(1/p,1/y),e.rotate(-u),e.translate(-l.x,-l.y)}}static pathZ(e){e.current=e.start}pathZ(e,t){A.pathZ(this.pathParser),e&&t.x1!==t.x2&&t.y1!==t.y2&&e.closePath()}}class bl extends A{constructor(e,t,r){super(e,t,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class Ce extends Ge{constructor(e,t,r){super(e,t,new.target===Ce?!0:r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;super.setContext(e,t);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(e.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(e){if(this.type!=="text")return this.getTElementBoundingBox(e);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e);var t=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(e,this,this,i);t?t.addBoundingBox(n):t=n}),t}getFontSize(){var{document:e,parent:t}=this,r=Z.parse(e.ctx.font).fontSize,i=t.getStyle("font-size").getNumber(r);return i}getTElementBoundingBox(e){var t=this.getFontSize();return new ce(this.x,this.y-t,this.x+this.measureText(e),this.y)}getGlyph(e,t,r){var i=t[r],n=null;if(e.isArabic){var o=t.length,s=t[r-1],u=t[r+1],l="isolated";if((r===0||s===" ")&&r<o-1&&u!==" "&&(l="terminal"),r>0&&s!==" "&&r<o-1&&u!==" "&&(l="medial"),r>0&&s!==" "&&(r===o-1||u===" ")&&(l="initial"),typeof e.glyphs[i]<"u"){var h=e.glyphs[i];n=h instanceof bl?h:h[l]}}else n=e.glyphs[i];return n||(n=e.missingGlyph),n}getText(){return""}getTextFromNode(e){var t=e||this.node,r=Array.from(t.parentNode.childNodes),i=r.indexOf(t),n=r.length-1,o=lt(t.textContent||"");return i===0&&(o=n1(o)),i===n&&(o=s1(o)),o}renderChildren(e){if(this.type!=="text"){this.renderTElementChildren(e);return}this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e),this.children.forEach((r,i)=>{this.renderChild(e,this,this,i)});var{mouse:t}=this.document.screen;t.isWorking()&&t.checkBoundingBox(this,this.getBoundingBox(e))}renderTElementChildren(e){var{document:t,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n){for(var{unitsPerEm:o}=n.fontFace,s=Z.parse(t.ctx.font),u=r.getStyle("font-size").getNumber(s.fontSize),l=r.getStyle("font-style").getString(s.fontStyle),h=u/o,f=n.isRTL?i.split("").reverse().join(""):i,c=ne(r.getAttribute("dx").getString()),v=f.length,g=0;g<v;g++){var d=this.getGlyph(n,f,g);e.translate(this.x,this.y),e.scale(h,-h);var p=e.lineWidth;e.lineWidth=e.lineWidth*o/u,l==="italic"&&e.transform(1,0,.4,1,0,0),d.render(e),l==="italic"&&e.transform(1,0,-.4,1,0,0),e.lineWidth=p,e.scale(1/h,-1/h),e.translate(-this.x,-this.y),this.x+=u*(d.horizAdvX||n.horizAdvX)/o,typeof c[g]<"u"&&!isNaN(c[g])&&(this.x+=c[g])}return}var{x:y,y:x}=this;e.fillStyle&&e.fillText(i,y,x),e.strokeStyle&&e.strokeText(i,y,x)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var e=this.leafTexts[this.textChunkStart],t=e.getStyle("text-anchor").getString("start"),r=!1,i=0;t==="start"&&!r||t==="end"&&r?i=e.x-this.minX:t==="end"&&!r||t==="start"&&r?i=e.x-this.maxX:i=e.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=i;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(e){this.children.forEach((t,r)=>{this.adjustChildCoordinatesRecursiveCore(e,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(e,t,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((o,s)=>{t.adjustChildCoordinatesRecursiveCore(e,t,n,s)}):this.adjustChildCoordinates(e,t,r,i)}adjustChildCoordinates(e,t,r,i){var n=r.children[i];if(typeof n.measureText!="function")return n;e.save(),n.setContext(e,!0);var o=n.getAttribute("x"),s=n.getAttribute("y"),u=n.getAttribute("dx"),l=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),f=!!h&&h.isRTL;i===0&&(o.hasValue()||o.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),u.hasValue()||u.setValue(n.getInheritedAttribute("dx")),l.hasValue()||l.setValue(n.getInheritedAttribute("dy")));var c=n.measureText(e);return f&&(t.x-=c),o.hasValue()?(t.applyAnchoring(),n.x=o.getPixels("x"),u.hasValue()&&(n.x+=u.getPixels("x"))):(u.hasValue()&&(t.x+=u.getPixels("x")),n.x=t.x),t.x=n.x,f||(t.x+=c),s.hasValue()?(n.y=s.getPixels("y"),l.hasValue()&&(n.y+=l.getPixels("y"))):(l.hasValue()&&(t.y+=l.getPixels("y")),n.y=t.y),t.y=n.y,t.leafTexts.push(n),t.minX=Math.min(t.minX,n.x,n.x+c),t.maxX=Math.max(t.maxX,n.x,n.x+c),n.clearContext(e),e.restore(),n}getChildBoundingBox(e,t,r,i){var n=r.children[i];if(typeof n.getBoundingBox!="function")return null;var o=n.getBoundingBox(e);return o?(n.children.forEach((s,u)=>{var l=t.getChildBoundingBox(e,t,n,u);o.addBoundingBox(l)}),o):null}renderChild(e,t,r,i){var n=r.children[i];n.render(e),n.children.forEach((o,s)=>{t.renderChild(e,t,n,s)})}measureText(e){var{measureCache:t}=this;if(~t)return t;var r=this.getText(),i=this.measureTargetText(e,r);return this.measureCache=i,i}measureTargetText(e,t){if(!t.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),o=i.isRTL?t.split("").reverse().join(""):t,s=ne(r.getAttribute("dx").getString()),u=o.length,l=0,h=0;h<u;h++){var f=this.getGlyph(i,o,h);l+=(f.horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,typeof s[h]<"u"&&!isNaN(s[h])&&(l+=s[h])}return l}if(!e.measureText)return t.length*10;e.save(),this.setContext(e,!0);var{width:c}=e.measureText(t);return this.clearContext(e),e.restore(),c}getInheritedAttribute(e){for(var t=this;t instanceof Ce&&t.isFirstChild();){var r=t.parent.getAttribute(e);if(r.hasValue(!0))return r.getValue("0");t=t.parent}return null}}class Lr extends Ce{constructor(e,t,r){super(e,t,new.target===Lr?!0:r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class _1 extends Lr{constructor(){super(...arguments),this.type="textNode"}}class Nt extends Ge{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(e){var t,{document:r}=this,{screen:i,window:n}=r,o=e.canvas;if(i.setDefaults(e),o.style&&typeof e.font<"u"&&n&&typeof n.getComputedStyle<"u"){e.font=n.getComputedStyle(o).getPropertyValue("font");var s=new S(r,"fontSize",Z.parse(e.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:l}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),f=this.getAttribute("refY"),c=this.getAttribute("viewBox"),v=c.hasValue()?ne(c.getString()):null,g=!this.root&&this.getStyle("overflow").getValue("hidden")!=="visible",d=0,p=0,y=0,x=0;v&&(d=v[0],p=v[1]),this.root||(u=this.getStyle("width").getPixels("x"),l=this.getStyle("height").getPixels("y"),this.type==="marker"&&(y=d,x=p,d=0,p=0)),i.viewPort.setCurrent(u,l),this.node&&(!this.parent||((t=this.node.parentNode)===null||t===void 0?void 0:t.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(e),e.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),v&&(u=v[2],l=v[3]),r.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:u,height:i.viewPort.height,desiredHeight:l,minX:d,minY:p,refX:h.getValue(),refY:f.getValue(),clip:g,clipX:y,clipY:x}),v&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(u,l))}clearContext(e){super.clearContext(e),this.document.screen.viewPort.removeCurrent()}resize(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),s=this.getAttribute("style"),u=i.getNumber(0),l=n.getNumber(0);if(r)if(typeof r=="string")this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(e),n.setValue(t),o.hasValue()||o.setValue("0 0 ".concat(u||e," ").concat(l||t)),s.hasValue()){var f=this.getStyle("width"),c=this.getStyle("height");f.hasValue()&&f.setValue("".concat(e,"px")),c.hasValue()&&c.setValue("".concat(t,"px"))}}}class xl extends A{constructor(){super(...arguments),this.type="rect"}path(e){var t=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),s=this.getAttribute("ry"),u=o.getPixels("x"),l=s.getPixels("y");if(o.hasValue()&&!s.hasValue()&&(l=u),s.hasValue()&&!o.hasValue()&&(u=l),u=Math.min(u,i/2),l=Math.min(l,n/2),e){var h=4*((Math.sqrt(2)-1)/3);e.beginPath(),n>0&&i>0&&(e.moveTo(t+u,r),e.lineTo(t+i-u,r),e.bezierCurveTo(t+i-u+h*u,r,t+i,r+l-h*l,t+i,r+l),e.lineTo(t+i,r+n-l),e.bezierCurveTo(t+i,r+n-l+h*l,t+i-u+h*u,r+n,t+i-u,r+n),e.lineTo(t+u,r+n),e.bezierCurveTo(t+u-h*u,r+n,t,r+n-l+h*l,t,r+n-l),e.lineTo(t,r+l),e.bezierCurveTo(t,r+l-h*l,t+u-h*u,r,t+u,r),e.closePath())}return new ce(t,r,t+i,r+n)}getMarkers(){return null}}class V1 extends A{constructor(){super(...arguments),this.type="circle"}path(e){var t=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return e&&i>0&&(e.beginPath(),e.arc(t,r,i,0,Math.PI*2,!1),e.closePath()),new ce(t-i,r-i,t+i,r+i)}getMarkers(){return null}}class D1 extends A{constructor(){super(...arguments),this.type="ellipse"}path(e){var t=4*((Math.sqrt(2)-1)/3),r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return e&&r>0&&i>0&&(e.beginPath(),e.moveTo(n+r,o),e.bezierCurveTo(n+r,o+t*i,n+t*r,o+i,n,o+i),e.bezierCurveTo(n-t*r,o+i,n-r,o+t*i,n-r,o),e.bezierCurveTo(n-r,o-t*i,n-t*r,o-i,n,o-i),e.bezierCurveTo(n+t*r,o-i,n+r,o-t*i,n+r,o),e.closePath()),new ce(n-r,o-i,n+r,o+i)}getMarkers(){return null}}class L1 extends A{constructor(){super(...arguments),this.type="line"}getPoints(){return[new k(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new k(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(e){var[{x:t,y:r},{x:i,y:n}]=this.getPoints();return e&&(e.beginPath(),e.moveTo(t,r),e.lineTo(i,n)),new ce(t,r,i,n)}getMarkers(){var[e,t]=this.getPoints(),r=e.angleTo(t);return[[e,r],[t,r]]}}class Tl extends A{constructor(e,t,r){super(e,t,r),this.type="polyline",this.points=[],this.points=k.parsePath(this.getAttribute("points").getString())}path(e){var{points:t}=this,[{x:r,y:i}]=t,n=new ce(r,i);return e&&(e.beginPath(),e.moveTo(r,i)),t.forEach(o=>{var{x:s,y:u}=o;n.addPoint(s,u),e&&e.lineTo(s,u)}),n}getMarkers(){var{points:e}=this,t=e.length-1,r=[];return e.forEach((i,n)=>{n!==t&&r.push([i,i.angleTo(e[n+1])])}),r.length>0&&r.push([e[e.length-1],r[r.length-1][1]]),r}}class k1 extends Tl{constructor(){super(...arguments),this.type="polygon"}path(e){var t=super.path(e),[{x:r,y:i}]=this.points;return e&&(e.lineTo(r,i),e.closePath()),t}}class B1 extends I{constructor(){super(...arguments),this.type="pattern"}createPattern(e,t,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),o=new Nt(this.document,null);o.attributes.viewBox=new S(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new S(this.document,"width","".concat(i,"px")),o.attributes.height=new S(this.document,"height","".concat(n,"px")),o.attributes.transform=new S(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var s=this.document.createCanvas(i,n),u=s.getContext("2d"),l=this.getAttribute("x"),h=this.getAttribute("y");l.hasValue()&&h.hasValue()&&u.translate(l.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var f=-1;f<=1;f++)for(var c=-1;c<=1;c++)u.save(),o.attributes.x=new S(this.document,"x",f*s.width),o.attributes.y=new S(this.document,"y",c*s.height),o.render(u),u.restore();var v=e.createPattern(s,"repeat");return v}}class j1 extends I{constructor(){super(...arguments),this.type="marker"}render(e,t,r){if(t){var{x:i,y:n}=t,o=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");e.translate(i,n),o==="auto"&&e.rotate(r),s==="strokeWidth"&&e.scale(e.lineWidth,e.lineWidth),e.save();var u=new Nt(this.document,null);u.type=this.type,u.attributes.viewBox=new S(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new S(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new S(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new S(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new S(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new S(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new S(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new S(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(e),e.restore(),s==="strokeWidth"&&e.scale(1/e.lineWidth,1/e.lineWidth),o==="auto"&&e.rotate(-r),e.translate(-i,-n)}}}class F1 extends I{constructor(){super(...arguments),this.type="defs"}render(){}}class rn extends Ge{constructor(){super(...arguments),this.type="g"}getBoundingBox(e){var t=new ce;return this.children.forEach(r=>{t.addBoundingBox(r.getBoundingBox(e))}),t}}class Ol extends I{constructor(e,t,r){super(e,t,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(o=>{o.type==="stop"&&i.push(o)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(e,t,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,o=this.getGradient(e,t);if(!o)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(p=>{o.addColorStop(p.offset,this.addParentOpacity(r,p.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:l}=s.screen,[h]=l.viewPorts,f=new xl(s,null);f.attributes.x=new S(s,"x",-u/3),f.attributes.y=new S(s,"y",-u/3),f.attributes.width=new S(s,"width",u),f.attributes.height=new S(s,"height",u);var c=new rn(s,null);c.attributes.transform=new S(s,"transform",this.getAttribute("gradientTransform").getValue()),c.children=[f];var v=new Nt(s,null);v.attributes.x=new S(s,"x",0),v.attributes.y=new S(s,"y",0),v.attributes.width=new S(s,"width",h.width),v.attributes.height=new S(s,"height",h.height),v.children=[c];var g=s.createCanvas(h.width,h.height),d=g.getContext("2d");return d.fillStyle=o,v.render(d),d.createPattern(g,"no-repeat")}return o}inheritStopContainer(e){this.attributesToInherit.forEach(t=>{!this.getAttribute(t).hasValue()&&e.getAttribute(t).hasValue()&&this.getAttribute(t,!0).setValue(e.getAttribute(t).getValue())})}addParentOpacity(e,t){if(e.hasValue()){var r=new S(this.document,"color",t);return r.addOpacity(e).getColor()}return t}}class U1 extends Ol{constructor(e,t,r){super(e,t,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(e,t){var r=this.getGradientUnits()==="objectBoundingBox",i=r?t.getBoundingBox(e):null;if(r&&!i)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&o===u?null:e.createLinearGradient(n,o,s,u)}}class G1 extends Ol{constructor(e,t,r){super(e,t,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(e,t){var r=this.getGradientUnits()==="objectBoundingBox",i=t.getBoundingBox(e);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,u=o;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var l=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return e.createRadialGradient(s,u,h,n,o,l)}}class z1 extends I{constructor(e,t,r){super(e,t,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),o=this.getStyle("stop-color",!0);o.getString()===""&&o.setValue("#000"),n.hasValue()&&(o=o.addOpacity(n)),this.offset=i,this.color=o.getColor()}}class an extends I{constructor(e,t,r){super(e,t,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,e.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new S(e,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var e=this.getAttribute("attributeType").getString(),t=this.getAttribute("attributeName").getString();return e==="CSS"?this.parent.getStyle(t,!0):this.parent.getAttribute(t,!0)}calcValue(){var{initialUnits:e}=this,{progress:t,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*t;return e==="%"&&(n*=100),"".concat(n).concat(e)}update(e){var{parent:t}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if(this.getAttribute("repeatCount").getString()==="indefinite"||this.getAttribute("repeatDur").getString()==="indefinite")this.duration=0;else if(i==="freeze"&&!this.frozen)this.frozen=!0,t.animationFrozen=!0,t.animationFrozenValue=r.getString();else if(i==="remove"&&!this.removed)return this.removed=!0,r.setValue(t.animationFrozen?t.animationFrozenValue:this.initialValue),!0;return!1}this.duration+=e;var n=!1;if(this.begin<this.duration){var o=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var u=s.getString();o="".concat(u,"(").concat(o,")")}r.setValue(o),n=!0}return n}getProgress(){var{document:e,values:t}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(t.hasValue()){var i=r.progress*(t.getValue().length-1),n=Math.floor(i),o=Math.ceil(i);r.from=new S(e,"from",parseFloat(t.getValue()[n])),r.to=new S(e,"to",parseFloat(t.getValue()[o])),r.progress=(i-n)/(o-n)}else r.from=this.from,r.to=this.to;return r}}class H1 extends an{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:e,from:t,to:r}=this.getProgress(),i=new di(t.getColor()),n=new di(r.getColor());if(i.ok&&n.ok){var o=i.r+(n.r-i.r)*e,s=i.g+(n.g-i.g)*e,u=i.b+(n.b-i.b)*e;return"rgb(".concat(Math.floor(o),", ").concat(Math.floor(s),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class Y1 extends an{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:e,from:t,to:r}=this.getProgress(),i=ne(t.getString()),n=ne(r.getString()),o=i.map((s,u)=>{var l=n[u];return s+(l-s)*e}).join(" ");return o}}class X1 extends I{constructor(e,t,r){super(e,t,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=e,{children:n}=this;for(var o of n)switch(o.type){case"font-face":{this.fontFace=o;var s=o.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break}case"missing-glyph":this.missingGlyph=o;break;case"glyph":{var u=o;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]=Object.create(null)),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u;break}}}render(){}}class W1 extends I{constructor(e,t,r){super(e,t,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class q1 extends A{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class Q1 extends Ce{constructor(){super(...arguments),this.type="tref"}getText(){var e=this.getHrefAttribute().getDefinition();if(e){var t=e.children[0];if(t)return t.getText()}return""}}class K1 extends Ce{constructor(e,t,r){super(e,t,r),this.type="a";var{childNodes:i}=t,n=i[0],o=i.length>0&&Array.from(i).every(s=>s.nodeType===3);this.hasText=o,this.text=o?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(e){if(this.hasText){super.renderChildren(e);var{document:t,x:r,y:i}=this,{mouse:n}=t.screen,o=new S(t,"fontSize",Z.parse(t.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new ce(r,i-o.getPixels("y"),r+this.measureText(e),i))}else if(this.children.length>0){var s=new rn(this.document,null);s.children=this.children,s.parent=this,s.render(e)}}onClick(){var{window:e}=this.document;e&&e.open(this.getHrefAttribute().getString())}onMouseMove(){var e=this.document.ctx;e.canvas.style.cursor="pointer"}}function Ao(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function ir(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Ao(Object(t),!0).forEach(function(r){en(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Ao(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}class Z1 extends Ce{constructor(e,t,r){super(e,t,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(e){var{dataArray:t}=this;e&&e.beginPath(),t.forEach(r=>{var{type:i,points:n}=r;switch(i){case w.LINE_TO:e&&e.lineTo(n[0],n[1]);break;case w.MOVE_TO:e&&e.moveTo(n[0],n[1]);break;case w.CURVE_TO:e&&e.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case w.QUAD_TO:e&&e.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case w.ARC:{var[o,s,u,l,h,f,c,v]=n,g=u>l?u:l,d=u>l?1:u/l,p=u>l?l/u:1;e&&(e.translate(o,s),e.rotate(c),e.scale(d,p),e.arc(0,0,g,h,h+f,!!(1-v)),e.scale(1/d,1/p),e.rotate(-c),e.translate(-o,-s));break}case w.CLOSE_PATH:e&&e.closePath();break}})}renderChildren(e){this.setTextData(e),e.save();var t=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=e.fillStyle;t==="underline"&&e.beginPath(),i.forEach((o,s)=>{var{p0:u,p1:l,rotation:h,text:f}=o;e.save(),e.translate(u.x,u.y),e.rotate(h),e.fillStyle&&e.fillText(f,0,0),e.strokeStyle&&e.strokeText(f,0,0),e.restore(),t==="underline"&&(s===0&&e.moveTo(u.x,u.y+r/8),e.lineTo(l.x,l.y+r/5))}),t==="underline"&&(e.lineWidth=r/20,e.strokeStyle=n,e.stroke(),e.closePath()),e.restore()}getLetterSpacingAt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.letterSpacingCache[e]||0}findSegmentToFitChar(e,t,r,i,n,o,s,u,l){var h=o,f=this.measureText(e,u);u===" "&&t==="justify"&&r<i&&(f+=(i-r)/n),l>-1&&(h+=this.getLetterSpacingAt(l));var c=this.textHeight/20,v=this.getEquidistantPointOnPath(h,c,0),g=this.getEquidistantPointOnPath(h+f,c,0),d={p0:v,p1:g},p=v&&g?Math.atan2(g.y-v.y,g.x-v.x):0;if(s){var y=Math.cos(Math.PI/2+p)*s,x=Math.cos(-p)*s;d.p0=ir(ir({},v),{},{x:v.x+y,y:v.y+x}),d.p1=ir(ir({},g),{},{x:g.x+y,y:g.y+x})}return h+=f,{offset:h,segment:d,rotation:p}}measureText(e,t){var{measuresCache:r}=this,i=t||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(e,i);return r.set(i,n),n}setTextData(e){if(!this.glyphInfo){var t=this.getText(),r=t.split(""),i=t.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(T=>T.getPixels("x")),o=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),l=this.parent.getStyle("letter-spacing"),h=0;!u.hasValue()||u.getValue()==="inherit"?h=l.getPixels():u.hasValue()&&u.getValue()!=="initial"&&u.getValue()!=="unset"&&(h=u.getPixels());var f=[],c=t.length;this.letterSpacingCache=f;for(var v=0;v<c;v++)f.push(typeof n[v]<"u"?n[v]:h);var g=f.reduce((T,$,E)=>E===0?0:T+$||0,0),d=this.measureText(e),p=Math.max(d+g,0);this.textWidth=d,this.textHeight=this.getFontSize(),this.glyphInfo=[];var y=this.getPathLength(),x=this.getStyle("startOffset").getNumber(0)*y,b=0;(s==="middle"||s==="center")&&(b=-p/2),(s==="end"||s==="right")&&(b=-p),b+=x,r.forEach((T,$)=>{var{offset:E,segment:O,rotation:C}=this.findSegmentToFitChar(e,s,p,y,i,b,o,T,$);b=E,!(!O.p0||!O.p1)&&this.glyphInfo.push({text:r[$],p0:O.p0,p1:O.p1,rotation:C})})}}parsePathData(e){if(this.pathLength=-1,!e)return[];var t=[],{pathParser:r}=e;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,o=i?i.y:0,s=r.next(),u=s.type,l=[];switch(s.type){case w.MOVE_TO:this.pathM(r,l);break;case w.LINE_TO:u=this.pathL(r,l);break;case w.HORIZ_LINE_TO:u=this.pathH(r,l);break;case w.VERT_LINE_TO:u=this.pathV(r,l);break;case w.CURVE_TO:this.pathC(r,l);break;case w.SMOOTH_CURVE_TO:u=this.pathS(r,l);break;case w.QUAD_TO:this.pathQ(r,l);break;case w.SMOOTH_QUAD_TO:u=this.pathT(r,l);break;case w.ARC:l=this.pathA(r);break;case w.CLOSE_PATH:A.pathZ(r);break}s.type!==w.CLOSE_PATH?t.push({type:u,points:l,start:{x:n,y:o},pathLength:this.calcLength(n,o,u,l)}):t.push({type:w.CLOSE_PATH,points:[],pathLength:0})}return t}pathM(e,t){var{x:r,y:i}=A.pathM(e).point;t.push(r,i)}pathL(e,t){var{x:r,y:i}=A.pathL(e).point;return t.push(r,i),w.LINE_TO}pathH(e,t){var{x:r,y:i}=A.pathH(e).point;return t.push(r,i),w.LINE_TO}pathV(e,t){var{x:r,y:i}=A.pathV(e).point;return t.push(r,i),w.LINE_TO}pathC(e,t){var{point:r,controlPoint:i,currentPoint:n}=A.pathC(e);t.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(e,t){var{point:r,controlPoint:i,currentPoint:n}=A.pathS(e);return t.push(r.x,r.y,i.x,i.y,n.x,n.y),w.CURVE_TO}pathQ(e,t){var{controlPoint:r,currentPoint:i}=A.pathQ(e);t.push(r.x,r.y,i.x,i.y)}pathT(e,t){var{controlPoint:r,currentPoint:i}=A.pathT(e);return t.push(r.x,r.y,i.x,i.y),w.QUAD_TO}pathA(e){var{rX:t,rY:r,sweepFlag:i,xAxisRotation:n,centp:o,a1:s,ad:u}=A.pathA(e);return i===0&&u>0&&(u-=2*Math.PI),i===1&&u<0&&(u+=2*Math.PI),[o.x,o.y,t,r,s,u,n,i]}calcLength(e,t,r,i){var n=0,o=null,s=null,u=0;switch(r){case w.LINE_TO:return this.getLineLength(e,t,i[0],i[1]);case w.CURVE_TO:for(n=0,o=this.getPointOnCubicBezier(0,e,t,i[0],i[1],i[2],i[3],i[4],i[5]),u=.01;u<=1;u+=.01)s=this.getPointOnCubicBezier(u,e,t,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case w.QUAD_TO:for(n=0,o=this.getPointOnQuadraticBezier(0,e,t,i[0],i[1],i[2],i[3]),u=.01;u<=1;u+=.01)s=this.getPointOnQuadraticBezier(u,e,t,i[0],i[1],i[2],i[3]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case w.ARC:{n=0;var l=i[4],h=i[5],f=i[4]+h,c=Math.PI/180;if(Math.abs(l-f)<c&&(c=Math.abs(l-f)),o=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],l,0),h<0)for(u=l-c;u>f;u-=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;else for(u=l+c;u<f;u+=c)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],f,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),n}}return 0}getPointOnLine(e,t,r,i,n){var o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:t,s=arguments.length>6&&arguments[6]!==void 0?arguments[6]:r,u=(n-r)/(i-t+rt),l=Math.sqrt(e*e/(1+u*u));i<t&&(l*=-1);var h=u*l,f=null;if(i===t)f={x:o,y:s+h};else if((s-r)/(o-t+rt)===u)f={x:o+l,y:s+h};else{var c=0,v=0,g=this.getLineLength(t,r,i,n);if(g<rt)return null;var d=(o-t)*(i-t)+(s-r)*(n-r);d/=g*g,c=t+d*(i-t),v=r+d*(n-r);var p=this.getLineLength(o,s,c,v),y=Math.sqrt(e*e-p*p);l=Math.sqrt(y*y/(1+u*u)),i<t&&(l*=-1),h=u*l,f={x:c+l,y:v+h}}return f}getPointOnPath(e){var t=this.getPathLength(),r=0,i=null;if(e<-5e-5||e-5e-5>t)return null;var{dataArray:n}=this;for(var o of n){if(o&&(o.pathLength<5e-5||r+o.pathLength+5e-5<e)){r+=o.pathLength;continue}var s=e-r,u=0;switch(o.type){case w.LINE_TO:i=this.getPointOnLine(s,o.start.x,o.start.y,o.points[0],o.points[1],o.start.x,o.start.y);break;case w.ARC:{var l=o.points[4],h=o.points[5],f=o.points[4]+h;if(u=l+s/o.pathLength*h,h<0&&u<f||h>=0&&u>f)break;i=this.getPointOnEllipticalArc(o.points[0],o.points[1],o.points[2],o.points[3],u,o.points[6]);break}case w.CURVE_TO:u=s/o.pathLength,u>1&&(u=1),i=this.getPointOnCubicBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3],o.points[4],o.points[5]);break;case w.QUAD_TO:u=s/o.pathLength,u>1&&(u=1),i=this.getPointOnQuadraticBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3]);break}if(i)return i;break}return null}getLineLength(e,t,r,i){return Math.sqrt((r-e)*(r-e)+(i-t)*(i-t))}getPathLength(){return this.pathLength===-1&&(this.pathLength=this.dataArray.reduce((e,t)=>t.pathLength>0?e+t.pathLength:e,0)),this.pathLength}getPointOnCubicBezier(e,t,r,i,n,o,s,u,l){var h=u*To(e)+o*Oo(e)+i*So(e)+t*Eo(e),f=l*To(e)+s*Oo(e)+n*So(e)+r*Eo(e);return{x:h,y:f}}getPointOnQuadraticBezier(e,t,r,i,n,o,s){var u=o*$o(e)+i*wo(e)+t*Co(e),l=s*$o(e)+n*wo(e)+r*Co(e);return{x:u,y:l}}getPointOnEllipticalArc(e,t,r,i,n,o){var s=Math.cos(o),u=Math.sin(o),l={x:r*Math.cos(n),y:i*Math.sin(n)};return{x:e+(l.x*s-l.y*u),y:t+(l.x*u+l.y*s)}}buildEquidistantCache(e,t){var r=this.getPathLength(),i=t||.25,n=e||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var o=0,s=0;s<=r;s+=i){var u=this.getPointOnPath(s),l=this.getPointOnPath(s+i);!u||!l||(o+=this.getLineLength(u.x,u.y,l.x,l.y),o>=n&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:s}),o-=n))}}}getEquidistantPointOnPath(e,t,r){if(this.buildEquidistantCache(t,r),e<0||e-this.getPathLength()>5e-5)return null;var i=Math.round(e/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var J1=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class e2 extends Ge{constructor(e,t,r){super(e,t,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);e.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(e){var t=this;return xe(function*(){try{var r=yield t.document.createImage(e);t.image=r}catch(i){console.error('Error while loading image "'.concat(e,'":'),i)}t.loaded=!0})()}loadSvg(e){var t=this;return xe(function*(){var r=J1.exec(e);if(r){var i=r[5];r[4]==="base64"?t.image=atob(i):t.image=decodeURIComponent(i)}else try{var n=yield t.document.fetch(e),o=yield n.text();t.image=o}catch(s){console.error('Error while loading image "'.concat(e,'":'),s)}t.loaded=!0})()}renderChildren(e){var{document:t,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!(!i||!r||!s||!u)){if(e.save(),e.translate(n,o),this.isSvg){var l=t.canvg.forkString(e,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:u});l.document.documentElement.parent=this,l.render()}else{var h=this.image;t.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:u,desiredHeight:h.height}),this.loaded&&(typeof h.complete>"u"||h.complete)&&e.drawImage(h,0,0)}e.restore()}}getBoundingBox(){var e=this.getAttribute("x").getPixels("x"),t=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new ce(e,t,e+r,t+i)}}class t2 extends Ge{constructor(){super(...arguments),this.type="symbol"}render(e){}}class r2{constructor(e){this.document=e,this.loaded=!1,e.fonts.push(this)}load(e,t){var r=this;return xe(function*(){try{var{document:i}=r,n=yield i.canvg.parser.load(t),o=n.getElementsByTagName("font");Array.from(o).forEach(s=>{var u=i.createElement(s);i.definitions[e]=u})}catch(s){console.error('Error while loading font "'.concat(t,'":'),s)}r.loaded=!0})()}}class Sl extends I{constructor(e,t,r){super(e,t,r),this.type="style";var i=lt(Array.from(t.childNodes).map(o=>o.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")),n=i.split("}");n.forEach(o=>{var s=o.trim();if(s){var u=s.split("{"),l=u[0].split(","),h=u[1].split(";");l.forEach(f=>{var c=f.trim();if(c){var v=e.styles[c]||{};if(h.forEach(p=>{var y=p.indexOf(":"),x=p.substr(0,y).trim(),b=p.substr(y+1,p.length-y).trim();x&&b&&(v[x]=new S(e,x,b))}),e.styles[c]=v,e.stylesSpecificity[c]=y1(c),c==="@font-face"){var g=v["font-family"].getString().replace(/"|'/g,""),d=v.src.getString().split(",");d.forEach(p=>{if(p.indexOf('format("svg")')>0){var y=gl(p);y&&new r2(e).load(g,y)}})}}})}})}}Sl.parseExternalUrl=gl;class a2 extends Ge{constructor(){super(...arguments),this.type="use"}setContext(e){super.setContext(e);var t=this.getAttribute("x"),r=this.getAttribute("y");t.hasValue()&&e.translate(t.getPixels("x"),0),r.hasValue()&&e.translate(0,r.getPixels("y"))}path(e){var{element:t}=this;t&&t.path(e)}renderChildren(e){var{document:t,element:r}=this;if(r){var i=r;if(r.type==="symbol"&&(i=new Nt(t,null),i.attributes.viewBox=new S(t,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new S(t,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new S(t,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new S(t,"opacity",this.calculateOpacity())),i.type==="svg"){var n=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new S(t,"width",n.getString())),o.hasValue()&&(i.attributes.height=new S(t,"height",o.getString()))}var s=i.parent;i.parent=this,i.render(e),i.parent=s}}getBoundingBox(e){var{element:t}=this;return t?t.getBoundingBox(e):null}elementTransform(){var{document:e,element:t}=this;return Be.fromElement(e,t)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function nr(a,e,t,r,i,n){return a[t*r*4+e*4+n]}function sr(a,e,t,r,i,n,o){a[t*r*4+e*4+n]=o}function F(a,e,t){var r=a[e];return r*t}function me(a,e,t,r){return e+Math.cos(a)*t+Math.sin(a)*r}class El extends I{constructor(e,t,r){super(e,t,r),this.type="feColorMatrix";var i=ne(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":{var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break}case"hueRotate":{var o=i[0]*Math.PI/180;i=[me(o,.213,.787,-.213),me(o,.715,-.715,-.715),me(o,.072,-.072,.928),0,0,me(o,.213,-.213,.143),me(o,.715,.285,.14),me(o,.072,-.072,-.283),0,0,me(o,.213,-.213,-.787),me(o,.715,-.715,.715),me(o,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break}case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1];break}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(e,t,r,i,n){for(var{includeOpacity:o,matrix:s}=this,u=e.getImageData(0,0,i,n),l=0;l<n;l++)for(var h=0;h<i;h++){var f=nr(u.data,h,l,i,n,0),c=nr(u.data,h,l,i,n,1),v=nr(u.data,h,l,i,n,2),g=nr(u.data,h,l,i,n,3),d=F(s,0,f)+F(s,1,c)+F(s,2,v)+F(s,3,g)+F(s,4,1),p=F(s,5,f)+F(s,6,c)+F(s,7,v)+F(s,8,g)+F(s,9,1),y=F(s,10,f)+F(s,11,c)+F(s,12,v)+F(s,13,g)+F(s,14,1),x=F(s,15,f)+F(s,16,c)+F(s,17,v)+F(s,18,g)+F(s,19,1);o&&(d=0,p=0,y=0,x*=g/255),sr(u.data,h,l,i,n,0,d),sr(u.data,h,l,i,n,1,p),sr(u.data,h,l,i,n,2,y),sr(u.data,h,l,i,n,3,x)}e.clearRect(0,0,i,n),e.putImageData(u,0,0)}}class kr extends I{constructor(){super(...arguments),this.type="mask"}apply(e,t){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),o=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!o&&!s){var u=new ce;this.children.forEach(g=>{u.addBoundingBox(g.getBoundingBox(e))}),i=Math.floor(u.x1),n=Math.floor(u.y1),o=Math.floor(u.width),s=Math.floor(u.height)}var l=this.removeStyles(t,kr.ignoreStyles),h=r.createCanvas(i+o,n+s),f=h.getContext("2d");r.screen.setDefaults(f),this.renderChildren(f),new El(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(f,0,0,i+o,n+s);var c=r.createCanvas(i+o,n+s),v=c.getContext("2d");r.screen.setDefaults(v),t.render(v),v.globalCompositeOperation="destination-in",v.fillStyle=f.createPattern(h,"no-repeat"),v.fillRect(0,0,i+o,n+s),e.fillStyle=v.createPattern(c,"no-repeat"),e.fillRect(0,0,i+o,n+s),this.restoreStyles(t,l)}render(e){}}kr.ignoreStyles=["mask","transform","clip-path"];var Po=()=>{};class i2 extends I{constructor(){super(...arguments),this.type="clipPath"}apply(e){var{document:t}=this,r=Reflect.getPrototypeOf(e),{beginPath:i,closePath:n}=e;r&&(r.beginPath=Po,r.closePath=Po),Reflect.apply(i,e,[]),this.children.forEach(o=>{if(!(typeof o.path>"u")){var s=typeof o.elementTransform<"u"?o.elementTransform():null;s||(s=Be.fromElement(t,o)),s&&s.apply(e),o.path(e),r&&(r.closePath=n),s&&s.unapply(e)}}),Reflect.apply(n,e,[]),e.clip(),r&&(r.beginPath=i,r.closePath=n)}render(e){}}class Br extends I{constructor(){super(...arguments),this.type="filter"}apply(e,t){var{document:r,children:i}=this,n=t.getBoundingBox(e);if(n){var o=0,s=0;i.forEach(y=>{var x=y.extraFilterDistance||0;o=Math.max(o,x),s=Math.max(s,x)});var u=Math.floor(n.width),l=Math.floor(n.height),h=u+2*o,f=l+2*s;if(!(h<1||f<1)){var c=Math.floor(n.x),v=Math.floor(n.y),g=this.removeStyles(t,Br.ignoreStyles),d=r.createCanvas(h,f),p=d.getContext("2d");r.screen.setDefaults(p),p.translate(-c+o,-v+s),t.render(p),i.forEach(y=>{typeof y.apply=="function"&&y.apply(p,0,0,h,f)}),e.drawImage(d,0,0,h,f,c-o,v-s,h,f),this.restoreStyles(t,g)}}}render(e){}}Br.ignoreStyles=["filter","transform","clip-path"];class n2 extends I{constructor(e,t,r){super(e,t,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(e,t,r,i,n){}}class s2 extends I{constructor(){super(...arguments),this.type="feMorphology"}apply(e,t,r,i,n){}}class o2 extends I{constructor(){super(...arguments),this.type="feComposite"}apply(e,t,r,i,n){}}class u2 extends I{constructor(e,t,r){super(e,t,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(e,t,r,i,n){var{document:o,blurRadius:s}=this,u=o.window?o.window.document.body:null,l=e.canvas;l.id=o.getUniqueId(),u&&(l.style.display="none",u.appendChild(l)),t1(l,t,r,i,n,s),u&&u.removeChild(l)}}class l2 extends I{constructor(){super(...arguments),this.type="title"}}class h2 extends I{constructor(){super(...arguments),this.type="desc"}}var v2={svg:Nt,rect:xl,circle:V1,ellipse:D1,line:L1,polyline:Tl,polygon:k1,path:A,pattern:B1,marker:j1,defs:F1,linearGradient:U1,radialGradient:G1,stop:z1,animate:an,animateColor:H1,animateTransform:Y1,font:X1,"font-face":W1,"missing-glyph":q1,glyph:bl,text:Ce,tspan:Lr,tref:Q1,a:K1,textPath:Z1,image:e2,g:rn,symbol:t2,style:Sl,use:a2,mask:kr,clipPath:i2,filter:Br,feDropShadow:n2,feMorphology:s2,feComposite:o2,feColorMatrix:El,feGaussianBlur:u2,title:l2,desc:h2};function Ro(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function f2(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Ro(Object(t),!0).forEach(function(r){en(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):Ro(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}function c2(a,e){var t=document.createElement("canvas");return t.width=a,t.height=e,t}function g2(a){return bi.apply(this,arguments)}function bi(){return bi=xe(function*(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,t=document.createElement("img");return e&&(t.crossOrigin="Anonymous"),new Promise((r,i)=>{t.onload=()=>{r(t)},t.onerror=(n,o,s,u,l)=>{i(l)},t.src=a})}),bi.apply(this,arguments)}class $e{constructor(e){var{rootEmSize:t=12,emSize:r=12,createCanvas:i=$e.createCanvas,createImage:n=$e.createImage,anonymousCrossOrigin:o}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.canvg=e,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=t,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,o),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(e,t){return typeof t=="boolean"?(r,i)=>e(r,typeof i=="boolean"?i:t):e}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:e}=this;return e[e.length-1]}set emSize(e){var{emSizeStack:t}=this;t.push(e)}popEmSize(){var{emSizeStack:e}=this;e.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(e=>e.loaded)}isFontsLoaded(){return this.fonts.every(e=>e.loaded)}createDocumentElement(e){var t=this.createElement(e.documentElement);return t.root=!0,t.addStylesFromStyleDefinition(),this.documentElement=t,t}createElement(e){var t=e.nodeName.replace(/^[^:]+:/,""),r=$e.elementTypes[t];return typeof r<"u"?new r(this,e):new P1(this,e)}createTextNode(e){return new _1(this,e)}setViewBox(e){this.screen.setViewBox(f2({document:this},e))}}$e.createCanvas=c2;$e.createImage=g2;$e.elementTypes=v2;function No(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),t.push.apply(t,r)}return t}function De(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?No(Object(t),!0).forEach(function(r){en(a,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):No(Object(t)).forEach(function(r){Object.defineProperty(a,r,Object.getOwnPropertyDescriptor(t,r))})}return a}class mt{constructor(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.parser=new Ga(r),this.screen=new Dr(e,r),this.options=r;var i=new $e(this,r),n=i.createDocumentElement(t);this.document=i,this.documentElement=n}static from(e,t){var r=arguments;return xe(function*(){var i=r.length>2&&r[2]!==void 0?r[2]:{},n=new Ga(i),o=yield n.parse(t);return new mt(e,o,i)})()}static fromString(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=new Ga(r),n=i.parseFromString(t);return new mt(e,n,r)}fork(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return mt.from(e,t,De(De({},this.options),r))}forkString(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return mt.fromString(e,t,De(De({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var e=arguments,t=this;return xe(function*(){var r=e.length>0&&e[0]!==void 0?e[0]:{};t.start(De({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield t.ready(),t.stop()})()}start(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{documentElement:t,screen:r,options:i}=this;r.start(t,De(De({enableRedraw:!0},i),e))}stop(){this.screen.stop()}resize(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.documentElement.resize(e,t,r)}}export{K1 as AElement,H1 as AnimateColorElement,an as AnimateElement,Y1 as AnimateTransformElement,ce as BoundingBox,To as CB1,Oo as CB2,So as CB3,Eo as CB4,mt as Canvg,V1 as CircleElement,i2 as ClipPathElement,F1 as DefsElement,h2 as DescElement,$e as Document,I as Element,D1 as EllipseElement,El as FeColorMatrixElement,o2 as FeCompositeElement,n2 as FeDropShadowElement,u2 as FeGaussianBlurElement,s2 as FeMorphologyElement,Br as FilterElement,Z as Font,X1 as FontElement,W1 as FontFaceElement,rn as GElement,bl as GlyphElement,Ol as GradientElement,e2 as ImageElement,L1 as LineElement,U1 as LinearGradientElement,j1 as MarkerElement,kr as MaskElement,yl as Matrix,q1 as MissingGlyphElement,b1 as Mouse,rt as PSEUDO_ZERO,Ga as Parser,A as PathElement,w as PathParser,B1 as PatternElement,k as Point,k1 as PolygonElement,Tl as PolylineElement,S as Property,$o as QB1,wo as QB2,Co as QB3,G1 as RadialGradientElement,xl as RectElement,Ge as RenderedElement,S1 as Rotate,Nt as SVGElement,r2 as SVGFontLoader,E1 as Scale,Dr as Screen,ml as Skew,$1 as SkewX,w1 as SkewY,z1 as StopElement,Sl as StyleElement,t2 as SymbolElement,Q1 as TRefElement,Lr as TSpanElement,Ce as TextElement,Z1 as TextPathElement,l2 as TitleElement,Be as Transform,O1 as Translate,P1 as UnknownElement,a2 as UseElement,m1 as ViewPort,lt as compressSpaces,mt as default,y1 as getSelectorSpecificity,u1 as normalizeAttributeName,l1 as normalizeColor,gl as parseExternalUrl,p2 as presets,ne as toNumbers,n1 as trimLeft,s1 as trimRight,bo as vectorMagnitude,xo as vectorsAngle,mi as vectorsRatio};
