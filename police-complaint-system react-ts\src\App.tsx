import { useState } from 'react'
import {
  Home,
  FileText,
  List,
  BarChart3,
  Settings,
  Plus,
  Search,
  Bell
} from 'lucide-react'
import NewComplaintForm from './components/NewComplaintForm'
import AllComplaintsView from './components/AllComplaintsView'
import ReportsView from './components/ReportsView'
import StatisticsDashboard from './components/StatisticsDashboard'

function App() {
  const [activeView, setActiveView] = useState('dashboard')
  const currentDate = new Date().toLocaleDateString('fr-FR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const navigationItems = [
    { id: 'dashboard', label: 'Tableau de Bord', labelAr: 'لوحة التحكم', icon: Home },
    { id: 'new-complaint', label: 'Nouvelle Plainte', labelAr: 'شكوى جديدة', icon: Plus },
    { id: 'all-complaints', label: 'Toutes les Plaintes', labelAr: 'جميع الشكاوى', icon: List },
    { id: 'reports', label: 'Rapports', labelAr: 'التقارير', icon: FileText },
    { id: 'statistics', label: 'Statistiques', labelAr: 'الإحصائيات', icon: BarChart3 },
    { id: 'settings', label: 'Paramètres', labelAr: 'الإعدادات', icon: Settings }
  ]

  const renderMainContent = () => {
    switch (activeView) {
      case 'dashboard':
        return <StatisticsDashboard />
      case 'new-complaint':
        return <NewComplaintForm />
      case 'all-complaints':
        return <AllComplaintsView />
      case 'reports':
        return <ReportsView />
      case 'statistics':
        return <StatisticsDashboard />
      case 'settings':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Paramètres</h2>
            <div className="card">
              <p className="text-gray-500">Les paramètres de l'application seront configurés ici...</p>
            </div>
          </div>
        )
      default:
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Page non trouvée</h2>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">Police Nationale</h1>
          <p className="text-sm text-gray-600">République du Tchad</p>
        </div>

        <nav className="mt-6">
          {navigationItems.map((item) => {
            const Icon = item.icon
            return (
              <button
                key={item.id}
                onClick={() => setActiveView(item.id)}
                className={`w-full nav-item ${activeView === item.id ? 'active' : ''}`}
              >
                <Icon className="w-5 h-5 mr-3" />
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium">{item.label}</div>
                  <div className="text-xs text-gray-500 text-arabic">{item.labelAr}</div>
                </div>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="police-header">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold">Système de Gestion des Plaintes</h1>
              <p className="text-sm opacity-90">{currentDate}</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 hover:bg-blue-800 rounded-md transition-colors">
                <Search className="w-5 h-5" />
              </button>
              <button className="p-2 hover:bg-blue-800 rounded-md transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <div className="text-right">
                <p className="text-sm font-medium">Agent de Police</p>
                <p className="text-xs opacity-90">En Service</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto">
          {renderMainContent()}
        </main>
      </div>
    </div>
  )
}

export default App
