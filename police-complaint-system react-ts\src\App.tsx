import { useState } from 'react'
import {
  Home,
  FileText,
  List,
  BarChart3,
  Settings,
  Plus,
  Search,
  Bell
} from 'lucide-react'

function App() {
  const [activeView, setActiveView] = useState('dashboard')
  const currentDate = new Date().toLocaleDateString('fr-FR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const navigationItems = [
    { id: 'dashboard', label: 'Tableau de Bord', labelAr: 'لوحة التحكم', icon: Home },
    { id: 'new-complaint', label: 'Nouvelle Plainte', labelAr: 'شكوى جديدة', icon: Plus },
    { id: 'all-complaints', label: 'Toutes les Plaintes', labelAr: 'جميع الشكاوى', icon: List },
    { id: 'reports', label: 'Rapports', labelAr: 'التقارير', icon: FileText },
    { id: 'statistics', label: 'Statistiques', labelAr: 'الإحصائيات', icon: BarChart3 },
    { id: 'settings', label: 'Paramètres', labelAr: 'الإعدادات', icon: Settings }
  ]

  const renderMainContent = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Tableau de Bord</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Plaintes en Attente</h3>
                <p className="text-3xl font-bold text-yellow-600">12</p>
              </div>
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">En Cours d'Investigation</h3>
                <p className="text-3xl font-bold text-blue-600">8</p>
              </div>
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Résolues</h3>
                <p className="text-3xl font-bold text-green-600">45</p>
              </div>
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Total ce Mois</h3>
                <p className="text-3xl font-bold text-gray-600">65</p>
              </div>
            </div>
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Plaintes Récentes</h3>
              <p className="text-gray-500">Les plaintes récentes apparaîtront ici...</p>
            </div>
          </div>
        )
      case 'new-complaint':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Nouvelle Plainte</h2>
            <div className="card">
              <p className="text-gray-500">Le formulaire de nouvelle plainte sera implémenté ici...</p>
            </div>
          </div>
        )
      case 'all-complaints':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Toutes les Plaintes</h2>
            <div className="card">
              <p className="text-gray-500">La liste des plaintes sera affichée ici...</p>
            </div>
          </div>
        )
      case 'reports':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Rapports</h2>
            <div className="card">
              <p className="text-gray-500">Les rapports PDF seront générés ici...</p>
            </div>
          </div>
        )
      case 'statistics':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Statistiques</h2>
            <div className="card">
              <p className="text-gray-500">Les graphiques et statistiques seront affichés ici...</p>
            </div>
          </div>
        )
      case 'settings':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Paramètres</h2>
            <div className="card">
              <p className="text-gray-500">Les paramètres de l'application seront configurés ici...</p>
            </div>
          </div>
        )
      default:
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Page non trouvée</h2>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">Police Nationale</h1>
          <p className="text-sm text-gray-600">République du Tchad</p>
        </div>

        <nav className="mt-6">
          {navigationItems.map((item) => {
            const Icon = item.icon
            return (
              <button
                key={item.id}
                onClick={() => setActiveView(item.id)}
                className={`w-full nav-item ${activeView === item.id ? 'active' : ''}`}
              >
                <Icon className="w-5 h-5 mr-3" />
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium">{item.label}</div>
                  <div className="text-xs text-gray-500 text-arabic">{item.labelAr}</div>
                </div>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="police-header">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold">Système de Gestion des Plaintes</h1>
              <p className="text-sm opacity-90">{currentDate}</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 hover:bg-blue-800 rounded-md transition-colors">
                <Search className="w-5 h-5" />
              </button>
              <button className="p-2 hover:bg-blue-800 rounded-md transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <div className="text-right">
                <p className="text-sm font-medium">Agent de Police</p>
                <p className="text-xs opacity-90">En Service</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto">
          {renderMainContent()}
        </main>
      </div>
    </div>
  )
}

export default App
