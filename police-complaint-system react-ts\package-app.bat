@echo off
echo Packaging Police Complaint System...

REM Build the application first
echo Building application...
call npm run build
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b %errorlevel%
)

REM Create package directory
if exist "Police-Complaint-System-Package" rmdir /s /q "Police-Complaint-System-Package"
mkdir "Police-Complaint-System-Package"

REM Copy necessary files
echo Copying application files...
xcopy /E /I "dist" "Police-Complaint-System-Package\dist"
xcopy /E /I "dist-electron" "Police-Complaint-System-Package\dist-electron"
copy "package.json" "Police-Complaint-System-Package\"

REM Copy node_modules (only essential ones)
mkdir "Police-Complaint-System-Package\node_modules"
xcopy /E /I "node_modules\electron" "Police-Complaint-System-Package\node_modules\electron"
xcopy /E /I "node_modules\sql.js" "Police-Complaint-System-Package\node_modules\sql.js"

REM Create run script
echo Creating run script...
echo @echo off > "Police-Complaint-System-Package\run.bat"
echo echo Starting Police Complaint System... >> "Police-Complaint-System-Package\run.bat"
echo node_modules\electron\dist\electron.exe . >> "Police-Complaint-System-Package\run.bat"

REM Create README
echo Creating README...
echo Police Complaint System - République du Tchad > "Police-Complaint-System-Package\README.txt"
echo. >> "Police-Complaint-System-Package\README.txt"
echo To run the application: >> "Police-Complaint-System-Package\README.txt"
echo 1. Double-click on run.bat >> "Police-Complaint-System-Package\README.txt"
echo 2. Or run: node_modules\electron\dist\electron.exe . >> "Police-Complaint-System-Package\README.txt"
echo. >> "Police-Complaint-System-Package\README.txt"
echo The application will create a database automatically in your user data folder. >> "Police-Complaint-System-Package\README.txt"

echo Package created successfully in Police-Complaint-System-Package folder!
echo You can now distribute this folder or create a ZIP file from it.
pause
