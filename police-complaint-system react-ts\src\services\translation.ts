// Translation service for Arabic-French police terminology
// This is a basic implementation - can be enhanced with more sophisticated translation

export interface TranslationDictionary {
  [key: string]: string;
}

// French to Arabic dictionary for police terms
const frenchToArabic: TranslationDictionary = {
  // Basic terms
  'vol': 'سرقة',
  'agression': 'اعتداء',
  'fraude': 'احتيال',
  'harcèlement': 'مضايقة',
  'dispute': 'نزاع',
  'vandalisme': 'تخريب',
  'autre': 'أخرى',
  
  // Status terms
  'en attente': 'في الانتظار',
  'en cours': 'قيد التحقيق',
  'résolu': 'محلول',
  'fermé': 'مغلق',
  
  // Priority terms
  'faible': 'منخفض',
  'moyen': 'متوسط',
  'élevé': 'عالي',
  'urgent': 'عاجل',
  
  // Common phrases
  'plainte déposée': 'شكوى مقدمة',
  'lieu de l\'incident': 'مكان الحادث',
  'date de l\'incident': 'تاريخ الحادث',
  'nom du plaignant': 'اسم المشتكي',
  'adresse': 'العنوان',
  'téléphone': 'الهاتف',
  'numéro d\'identité': 'رقم الهوية',
  'description': 'الوصف',
  'notes': 'ملاحظات',
  'officier responsable': 'الضابط المسؤول',
  
  // Common words
  'et': 'و',
  'le': 'ال',
  'la': 'ال',
  'les': 'ال',
  'de': 'من',
  'du': 'من ال',
  'des': 'من ال',
  'à': 'إلى',
  'au': 'إلى ال',
  'aux': 'إلى ال',
  'dans': 'في',
  'sur': 'على',
  'avec': 'مع',
  'pour': 'لـ',
  'par': 'بواسطة',
  'sans': 'بدون',
  'sous': 'تحت',
  'vers': 'نحو',
  'chez': 'عند',
  'depuis': 'منذ',
  'pendant': 'أثناء',
  'avant': 'قبل',
  'après': 'بعد',
  'entre': 'بين',
  'contre': 'ضد',
  'selon': 'حسب',
  'malgré': 'رغم',
  'sauf': 'عدا',
  'hormis': 'باستثناء'
};

// Arabic to French dictionary (reverse mapping)
const arabicToFrench: TranslationDictionary = Object.fromEntries(
  Object.entries(frenchToArabic).map(([fr, ar]) => [ar, fr])
);

export class TranslationService {
  private static instance: TranslationService;
  
  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  /**
   * Detect if text is primarily Arabic or French
   */
  public detectLanguage(text: string): 'arabic' | 'french' | 'mixed' {
    if (!text || text.trim().length === 0) return 'french';
    
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const arabicChars = (text.match(arabicRegex) || []).length;
    const totalChars = text.replace(/\s/g, '').length;
    
    if (arabicChars === 0) return 'french';
    if (arabicChars === totalChars) return 'arabic';
    return 'mixed';
  }

  /**
   * Translate text from French to Arabic
   */
  public translateToArabic(text: string): string {
    if (!text) return '';
    
    const language = this.detectLanguage(text);
    if (language === 'arabic') return text; // Already Arabic
    
    return this.performTranslation(text, frenchToArabic);
  }

  /**
   * Translate text from Arabic to French
   */
  public translateToFrench(text: string): string {
    if (!text) return '';
    
    const language = this.detectLanguage(text);
    if (language === 'french') return text; // Already French
    
    return this.performTranslation(text, arabicToFrench);
  }

  /**
   * Get both translations of a text
   */
  public getBothTranslations(text: string): { french: string; arabic: string } {
    if (!text) return { french: '', arabic: '' };
    
    const language = this.detectLanguage(text);
    
    switch (language) {
      case 'arabic':
        return {
          arabic: text,
          french: this.translateToFrench(text)
        };
      case 'french':
        return {
          french: text,
          arabic: this.translateToArabic(text)
        };
      case 'mixed':
        return {
          french: this.translateToFrench(text),
          arabic: this.translateToArabic(text)
        };
      default:
        return { french: text, arabic: text };
    }
  }

  /**
   * Perform the actual translation using the dictionary
   */
  private performTranslation(text: string, dictionary: TranslationDictionary): string {
    let translatedText = text.toLowerCase();
    
    // Sort by length (longest first) to handle phrases before individual words
    const sortedKeys = Object.keys(dictionary).sort((a, b) => b.length - a.length);
    
    for (const key of sortedKeys) {
      const regex = new RegExp(`\\b${this.escapeRegex(key)}\\b`, 'gi');
      translatedText = translatedText.replace(regex, dictionary[key]);
    }
    
    // If no translation found, return original text with a note
    if (translatedText === text.toLowerCase()) {
      return `${text} [ترجمة غير متوفرة]`;
    }
    
    return translatedText;
  }

  /**
   * Escape special regex characters
   */
  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Add new translation pair
   */
  public addTranslation(french: string, arabic: string): void {
    frenchToArabic[french.toLowerCase()] = arabic;
    arabicToFrench[arabic] = french.toLowerCase();
  }

  /**
   * Get available translations count
   */
  public getTranslationCount(): number {
    return Object.keys(frenchToArabic).length;
  }
}
