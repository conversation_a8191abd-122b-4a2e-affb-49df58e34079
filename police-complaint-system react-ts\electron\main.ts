import { app, <PERSON><PERSON><PERSON><PERSON>indow, ipc<PERSON>ain } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// Import database types only, we'll initialize database differently
import type { Co<PERSON>laint, ComplaintType } from '../src/database/database'

// Simple database setup
let db: any = null

function initializeDatabase() {
  return new Promise((resolve, reject) => {
    try {
      const sqlite3 = require('sqlite3').verbose()
      const fs = require('fs')

      // Get the user data directory for storing the database
      const userDataPath = app.getPath('userData')
      const dbPath = path.join(userDataPath, 'police_complaints.db')

      // Ensure the directory exists
      const dir = path.dirname(dbPath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err)
          reject(err)
          return
        }

        // Enable foreign keys
        db.run('PRAGMA foreign_keys = ON')

        // Create tables with schema - execute sequentially
        const executeStatement = (statement: string, callback: () => void) => {
          db.run(statement, (err) => {
            if (err) {
              console.error('Error executing statement:', err)
              reject(err)
              return
            }
            callback()
          })
        }

        // Step 1: Create complaint_types table
        executeStatement(`CREATE TABLE IF NOT EXISTS complaint_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name_fr TEXT NOT NULL,
          name_ar TEXT NOT NULL,
          color_code TEXT DEFAULT '#3b82f6',
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`, () => {

          // Step 2: Create complaints table
          executeStatement(`CREATE TABLE IF NOT EXISTS complaints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            complaint_number TEXT UNIQUE NOT NULL,
            date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
            complainant_name TEXT NOT NULL,
            complainant_address TEXT,
            complainant_phone TEXT,
            complainant_id_number TEXT,
            complaint_type_id INTEGER NOT NULL,
            description_ar TEXT,
            description_fr TEXT,
            location_incident TEXT NOT NULL,
            date_incident DATE NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
            evidence_notes TEXT,
            officer_notes TEXT,
            officer_in_charge TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )`, () => {

            // Step 3: Insert default complaint types
            executeStatement(`INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES
            (1, 'Vol', 'سرقة', '#ef4444'),
            (2, 'Agression', 'اعتداء', '#f97316'),
            (3, 'Fraude', 'احتيال', '#eab308'),
            (4, 'Harcèlement', 'مضايقة', '#8b5cf6'),
            (5, 'Dispute', 'نزاع', '#06b6d4'),
            (6, 'Vandalisme', 'تخريب', '#84cc16'),
            (7, 'Autre', 'أخرى', '#6b7280')`, () => {
              console.log('Database initialized successfully')
              resolve(true)
            })
          })
        })
      })
    } catch (error) {
      console.error('Failed to initialize database:', error)
      reject(error)
    }
  })
}

function generateComplaintNumber(): Promise<string> {
  return new Promise((resolve, reject) => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')

    // Get the count of complaints today
    const today = `${year}-${month}-${day}`
    db.get(`
      SELECT COUNT(*) as count
      FROM complaints
      WHERE DATE(date_registered) = DATE(?)
    `, [today], (err, row: any) => {
      if (err) {
        reject(err)
        return
      }

      const sequence = String((row?.count || 0) + 1).padStart(3, '0')
      resolve(`PL${year}${month}${day}${sequence}`)
    })
  })
}

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.mjs
// │
process.env.APP_ROOT = path.join(__dirname, '..')

// 🚧 Use ['ENV_NAME'] avoid vite:define plugin - Vite@2.x
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, 'public') : RENDERER_DIST

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.mjs'),
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL)
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(RENDERER_DIST, 'index.html'))
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Initialize database when app is ready
app.whenReady().then(async () => {
  try {
    await initializeDatabase()
  } catch (error) {
    console.error('Failed to initialize database:', error)
  }

  createWindow()
  setupIpcHandlers()
})

// Setup IPC handlers for database operations
function setupIpcHandlers() {
  // Get all complaint types
  ipcMain.handle('db:getComplaintTypes', async () => {
    return new Promise((resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')
        db.all('SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr', (err, rows) => {
          if (err) {
            console.error('Error getting complaint types:', err)
            reject(err)
          } else {
            resolve(rows)
          }
        })
      } catch (error) {
        console.error('Error getting complaint types:', error)
        reject(error)
      }
    })
  })

  // Create new complaint
  ipcMain.handle('db:createComplaint', async (event, complaint: Omit<Complaint, 'id'>) => {
    return new Promise(async (resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')

        // Generate complaint number if not provided
        if (!complaint.complaint_number) {
          complaint.complaint_number = await generateComplaintNumber()
        }

        const sql = `
          INSERT INTO complaints (
            complaint_number, complainant_name, complainant_address, complainant_phone,
            complainant_id_number, complaint_type_id, description_ar, description_fr,
            location_incident, date_incident, status, priority, evidence_notes,
            officer_notes, officer_in_charge
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `

        const params = [
          complaint.complaint_number,
          complaint.complainant_name,
          complaint.complainant_address,
          complaint.complainant_phone,
          complaint.complainant_id_number,
          complaint.complaint_type_id,
          complaint.description_ar,
          complaint.description_fr,
          complaint.location_incident,
          complaint.date_incident,
          complaint.status,
          complaint.priority,
          complaint.evidence_notes,
          complaint.officer_notes,
          complaint.officer_in_charge
        ]

        db.run(sql, params, function(err) {
          if (err) {
            console.error('Error creating complaint:', err)
            reject(err)
          } else {
            resolve({ id: this.lastID, ...complaint })
          }
        })
      } catch (error) {
        console.error('Error creating complaint:', error)
        reject(error)
      }
    })
  })

  // Get all complaints
  ipcMain.handle('db:getComplaints', async () => {
    return new Promise((resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')
        const sql = `
          SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          ORDER BY c.date_registered DESC
        `
        db.all(sql, (err, rows) => {
          if (err) {
            console.error('Error getting complaints:', err)
            reject(err)
          } else {
            resolve(rows)
          }
        })
      } catch (error) {
        console.error('Error getting complaints:', error)
        reject(error)
      }
    })
  })

  // Get complaint by ID
  ipcMain.handle('db:getComplaintById', async (event, id: number) => {
    return new Promise((resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')
        const sql = `
          SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          WHERE c.id = ?
        `
        db.get(sql, [id], (err, row) => {
          if (err) {
            console.error('Error getting complaint by ID:', err)
            reject(err)
          } else {
            resolve(row)
          }
        })
      } catch (error) {
        console.error('Error getting complaint by ID:', error)
        reject(error)
      }
    })
  })

  // Update complaint
  ipcMain.handle('db:updateComplaint', async (event, id: number, updates: Partial<Complaint>) => {
    return new Promise((resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')

        const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
        const values = [...Object.values(updates), id]

        const sql = `UPDATE complaints SET ${fields} WHERE id = ?`

        db.run(sql, values, function(err) {
          if (err) {
            console.error('Error updating complaint:', err)
            reject(err)
          } else {
            resolve(this.changes > 0)
          }
        })
      } catch (error) {
        console.error('Error updating complaint:', error)
        reject(error)
      }
    })
  })

  // Get complaint statistics
  ipcMain.handle('db:getComplaintStats', async () => {
    return new Promise((resolve, reject) => {
      try {
        if (!db) throw new Error('Database not initialized')

        let results = { statusStats: [], typeStats: [], monthlyStats: [], total: 0 }
        let completed = 0
        const totalQueries = 4

        // Get status counts
        db.all(`
          SELECT status, COUNT(*) as count
          FROM complaints
          GROUP BY status
        `, (err, rows) => {
          if (err) {
            reject(err)
            return
          }
          results.statusStats = rows
          completed++
          if (completed === totalQueries) resolve(results)
        })

        // Get type counts
        db.all(`
          SELECT ct.name_fr, COUNT(*) as count
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          GROUP BY c.complaint_type_id, ct.name_fr
        `, (err, rows) => {
          if (err) {
            reject(err)
            return
          }
          results.typeStats = rows
          completed++
          if (completed === totalQueries) resolve(results)
        })

        // Get monthly counts
        db.all(`
          SELECT
            strftime('%Y-%m', date_registered) as month,
            COUNT(*) as count
          FROM complaints
          WHERE date_registered >= date('now', '-12 months')
          GROUP BY strftime('%Y-%m', date_registered)
          ORDER BY month
        `, (err, rows) => {
          if (err) {
            reject(err)
            return
          }
          results.monthlyStats = rows
          completed++
          if (completed === totalQueries) resolve(results)
        })

        // Get total count
        db.get('SELECT COUNT(*) as count FROM complaints', (err, row: any) => {
          if (err) {
            reject(err)
            return
          }
          results.total = row?.count || 0
          completed++
          if (completed === totalQueries) resolve(results)
        })

      } catch (error) {
        console.error('Error getting complaint stats:', error)
        reject(error)
      }
    })
  })
}

// Clean up database connection on app quit
app.on('before-quit', () => {
  if (db) {
    db.close()
    db = null
  }
})
