{"name": "fill-range", "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "version": "7.1.1", "homepage": "https://github.com/jonschlinkert/fill-range", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/fill-range", "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "dependencies": {"to-regex-range": "^5.0.1"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.1", "nyc": "^15.1.0"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}