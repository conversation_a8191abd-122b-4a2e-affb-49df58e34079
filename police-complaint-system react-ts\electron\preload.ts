import { ipcRenderer, contextBridge } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('ipcRenderer', {
  on(...args: Parameters<typeof ipcRenderer.on>) {
    const [channel, listener] = args
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(...args: Parameters<typeof ipcRenderer.off>) {
    const [channel, ...omit] = args
    return ipcRenderer.off(channel, ...omit)
  },
  send(...args: Parameters<typeof ipcRenderer.send>) {
    const [channel, ...omit] = args
    return ipcRenderer.send(channel, ...omit)
  },
  invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
    const [channel, ...omit] = args
    return ipcRenderer.invoke(channel, ...omit)
  },
})

// Expose database API to renderer
contextBridge.exposeInMainWorld('electronAPI', {
  // Complaint operations
  getComplaintTypes: () => ipcRenderer.invoke('db:getComplaintTypes'),
  createComplaint: (complaint: any) => ipcRenderer.invoke('db:createComplaint', complaint),
  getComplaints: () => ipcRenderer.invoke('db:getComplaints'),
  getComplaintById: (id: number) => ipcRenderer.invoke('db:getComplaintById', id),
  updateComplaint: (id: number, updates: any) => ipcRenderer.invoke('db:updateComplaint', id, updates),
  getComplaintStats: () => ipcRenderer.invoke('db:getComplaintStats'),
})
