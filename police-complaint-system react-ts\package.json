{"name": "police-complaint-system-react-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "better-sqlite3": "^11.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.518.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zustand": "^5.0.5"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js"}