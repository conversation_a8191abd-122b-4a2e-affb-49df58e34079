import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Calendar,
  MapPin,
  User,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Activity,
  Loader,
  RefreshCw
} from 'lucide-react'

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complaint_type_id: number
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  officer_in_charge: string
  location_incident: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

interface ComplaintType {
  id: number
  name_fr: string
  name_ar: string
  color_code: string
}

interface StatisticsData {
  statusStats: Array<{ status: string; count: number }>
  typeStats: Array<{ name_fr: string; count: number }>
  monthlyStats: Array<{ month: string; count: number }>
  total: number
}

const StatisticsDashboard: React.FC = () => {
  const [complaints, setComplaints] = useState<Complaint[]>([])
  const [complaintTypes, setComplaintTypes] = useState<ComplaintType[]>([])
  const [statistics, setStatistics] = useState<StatisticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month')

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      const [complaintsData, typesData, statsData] = await Promise.all([
        window.electronAPI.getComplaints(),
        window.electronAPI.getComplaintTypes(),
        window.electronAPI.getComplaintStats()
      ])
      
      setComplaints(complaintsData)
      setComplaintTypes(typesData)
      setStatistics(statsData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate additional metrics
  const calculateMetrics = () => {
    if (!complaints.length) return null

    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const thisMonthComplaints = complaints.filter(c => 
      new Date(c.date_registered) >= lastMonth
    )
    const thisWeekComplaints = complaints.filter(c => 
      new Date(c.date_registered) >= lastWeek
    )

    const resolvedThisMonth = thisMonthComplaints.filter(c => c.status === 'resolved').length
    const pendingComplaints = complaints.filter(c => c.status === 'pending').length
    const urgentComplaints = complaints.filter(c => c.priority === 'urgent').length

    // Calculate resolution rate
    const totalResolved = complaints.filter(c => c.status === 'resolved').length
    const resolutionRate = complaints.length > 0 ? (totalResolved / complaints.length) * 100 : 0

    // Calculate average processing time (mock data for demo)
    const avgProcessingTime = 5.2 // days

    return {
      totalComplaints: complaints.length,
      thisMonthComplaints: thisMonthComplaints.length,
      thisWeekComplaints: thisWeekComplaints.length,
      resolvedThisMonth,
      pendingComplaints,
      urgentComplaints,
      resolutionRate,
      avgProcessingTime
    }
  }

  // Prepare chart data
  const prepareChartData = () => {
    if (!complaints.length || !statistics) return null

    // Status distribution for pie chart
    const statusData = [
      { name: 'En Attente', value: complaints.filter(c => c.status === 'pending').length, color: '#f59e0b' },
      { name: 'En Investigation', value: complaints.filter(c => c.status === 'investigating').length, color: '#3b82f6' },
      { name: 'Résolues', value: complaints.filter(c => c.status === 'resolved').length, color: '#10b981' },
      { name: 'Fermées', value: complaints.filter(c => c.status === 'closed').length, color: '#6b7280' }
    ].filter(item => item.value > 0)

    // Priority distribution
    const priorityData = [
      { name: 'Faible', value: complaints.filter(c => c.priority === 'low').length, color: '#10b981' },
      { name: 'Moyen', value: complaints.filter(c => c.priority === 'medium').length, color: '#f59e0b' },
      { name: 'Élevé', value: complaints.filter(c => c.priority === 'high').length, color: '#f97316' },
      { name: 'Urgent', value: complaints.filter(c => c.priority === 'urgent').length, color: '#ef4444' }
    ].filter(item => item.value > 0)

    // Monthly trend data
    const monthlyData = []
    for (let i = 11; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const monthKey = date.toISOString().slice(0, 7)
      const monthName = date.toLocaleDateString('fr-FR', { month: 'short', year: '2-digit' })
      
      const monthComplaints = complaints.filter(c => 
        c.date_registered.slice(0, 7) === monthKey
      )
      
      monthlyData.push({
        month: monthName,
        total: monthComplaints.length,
        resolved: monthComplaints.filter(c => c.status === 'resolved').length,
        pending: monthComplaints.filter(c => c.status === 'pending').length
      })
    }

    // Type distribution for bar chart
    const typeData = complaintTypes.map(type => ({
      name: type.name_fr,
      count: complaints.filter(c => c.complaint_type_id === type.id).length,
      color: type.color_code
    })).filter(item => item.count > 0)

    return { statusData, priorityData, monthlyData, typeData }
  }

  const metrics = calculateMetrics()
  const chartData = prepareChartData()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Chargement des statistiques...</span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Tableau de Bord</h2>
          <p className="text-sm text-gray-600">لوحة القيادة</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="form-input text-sm"
          >
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
            <option value="quarter">Ce trimestre</option>
            <option value="year">Cette année</option>
          </select>
          <button
            onClick={loadDashboardData}
            className="btn-secondary text-sm flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card hover:shadow-lg transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total des Plaintes</p>
              <p className="text-2xl font-bold text-gray-900">{metrics?.totalComplaints || 0}</p>
              <p className="text-xs text-gray-500">إجمالي الشكاوى</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-xs text-green-500">+{metrics?.thisMonthComplaints || 0} ce mois</span>
              </div>
            </div>
          </div>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">En Attente</p>
              <p className="text-2xl font-bold text-gray-900">{metrics?.pendingComplaints || 0}</p>
              <p className="text-xs text-gray-500">في الانتظار</p>
              <div className="flex items-center mt-2">
                <Clock className="w-4 h-4 text-yellow-500 mr-1" />
                <span className="text-xs text-yellow-600">Nécessite attention</span>
              </div>
            </div>
          </div>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Taux de Résolution</p>
              <p className="text-2xl font-bold text-gray-900">{metrics?.resolutionRate.toFixed(1) || 0}%</p>
              <p className="text-xs text-gray-500">معدل الحل</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-xs text-green-500">{metrics?.resolvedThisMonth || 0} résolues ce mois</span>
              </div>
            </div>
          </div>
        </div>

        <div className="card hover:shadow-lg transition-shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Urgentes</p>
              <p className="text-2xl font-bold text-gray-900">{metrics?.urgentComplaints || 0}</p>
              <p className="text-xs text-gray-500">عاجلة</p>
              <div className="flex items-center mt-2">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <span className="text-xs text-red-600">Action immédiate</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Cette Semaine</p>
              <p className="text-xl font-bold text-gray-900">{metrics?.thisWeekComplaints || 0}</p>
              <p className="text-xs text-gray-500">هذا الأسبوع</p>
            </div>
            <Calendar className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Temps Moyen</p>
              <p className="text-xl font-bold text-gray-900">{metrics?.avgProcessingTime || 0} jours</p>
              <p className="text-xs text-gray-500">متوسط الوقت</p>
            </div>
            <Activity className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Efficacité</p>
              <p className="text-xl font-bold text-gray-900">{(metrics?.resolutionRate || 0) > 70 ? 'Excellente' : (metrics?.resolutionRate || 0) > 50 ? 'Bonne' : 'À améliorer'}</p>
              <p className="text-xs text-gray-500">الكفاءة</p>
            </div>
            <BarChart3 className="w-8 h-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Status Distribution Pie Chart */}
        <div className="card">
          <div className="flex items-center mb-4">
            <PieChartIcon className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Répartition par Statut</h3>
            <span className="text-sm text-gray-500 ml-2">/ توزيع حسب الحالة</span>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={chartData?.statusData || []}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData?.statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Priority Distribution */}
        <div className="card">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Répartition par Priorité</h3>
            <span className="text-sm text-gray-500 ml-2">/ توزيع حسب الأولوية</span>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData?.priorityData || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Monthly Trend Chart */}
      <div className="card mb-8">
        <div className="flex items-center mb-4">
          <Activity className="w-5 h-5 text-gray-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Tendance Mensuelle</h3>
          <span className="text-sm text-gray-500 ml-2">/ الاتجاه الشهري</span>
        </div>
        <ResponsiveContainer width="100%" height={400}>
          <AreaChart data={chartData?.monthlyData || []}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area
              type="monotone"
              dataKey="total"
              stackId="1"
              stroke="#3b82f6"
              fill="#3b82f6"
              name="Total"
            />
            <Area
              type="monotone"
              dataKey="resolved"
              stackId="2"
              stroke="#10b981"
              fill="#10b981"
              name="Résolues"
            />
            <Area
              type="monotone"
              dataKey="pending"
              stackId="3"
              stroke="#f59e0b"
              fill="#f59e0b"
              name="En Attente"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Bottom Section - Type Distribution and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Type Distribution Chart */}
        <div className="card">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Répartition par Type</h3>
            <span className="text-sm text-gray-500 ml-2">/ توزيع حسب النوع</span>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData?.typeData || []} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" width={100} />
              <Tooltip />
              <Bar dataKey="count" fill="#6366f1" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Recent Complaints */}
        <div className="card">
          <div className="flex items-center mb-4">
            <Clock className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Plaintes Récentes</h3>
            <span className="text-sm text-gray-500 ml-2">/ الشكاوى الأخيرة</span>
          </div>
          <div className="space-y-3 max-h-72 overflow-y-auto">
            {complaints.slice(0, 5).map((complaint) => (
              <div key={complaint.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    complaint.status === 'pending' ? 'bg-yellow-500' :
                    complaint.status === 'investigating' ? 'bg-blue-500' :
                    complaint.status === 'resolved' ? 'bg-green-500' : 'bg-gray-500'
                  }`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{complaint.complaint_number}</p>
                    <p className="text-xs text-gray-500">{complaint.complainant_name}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">{new Date(complaint.date_registered).toLocaleDateString('fr-FR')}</p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    complaint.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                    complaint.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                    complaint.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {complaint.priority === 'urgent' ? 'Urgent' :
                     complaint.priority === 'high' ? 'Élevé' :
                     complaint.priority === 'medium' ? 'Moyen' : 'Faible'}
                  </span>
                </div>
              </div>
            ))}
            {complaints.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Aucune plainte enregistrée</p>
                <p className="text-sm">لا توجد شكاوى مسجلة</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Officer Performance Section */}
      <div className="card">
        <div className="flex items-center mb-4">
          <User className="w-5 h-5 text-gray-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Performance des Officiers</h3>
          <span className="text-sm text-gray-500 ml-2">/ أداء الضباط</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {(() => {
            // Calculate officer performance
            const officerStats = complaints.reduce((acc, complaint) => {
              const officer = complaint.officer_in_charge
              if (!acc[officer]) {
                acc[officer] = { total: 0, resolved: 0, pending: 0 }
              }
              acc[officer].total++
              if (complaint.status === 'resolved') acc[officer].resolved++
              if (complaint.status === 'pending') acc[officer].pending++
              return acc
            }, {} as Record<string, { total: number; resolved: number; pending: number }>)

            return Object.entries(officerStats)
              .sort(([,a], [,b]) => b.total - a.total)
              .slice(0, 3)
              .map(([officer, stats]) => (
                <div key={officer} className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <User className="w-4 h-4 text-gray-500 mr-2" />
                    <h4 className="font-medium text-gray-900">{officer}</h4>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Total:</span>
                      <span className="font-medium">{stats.total}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Résolues:</span>
                      <span className="font-medium text-green-600">{stats.resolved}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">En attente:</span>
                      <span className="font-medium text-yellow-600">{stats.pending}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Taux:</span>
                      <span className="font-medium text-blue-600">
                        {stats.total > 0 ? ((stats.resolved / stats.total) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                  </div>
                </div>
              ))
          })()}
          {complaints.length === 0 && (
            <div className="col-span-3 text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Aucune donnée de performance disponible</p>
              <p className="text-sm">لا توجد بيانات أداء متاحة</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default StatisticsDashboard
