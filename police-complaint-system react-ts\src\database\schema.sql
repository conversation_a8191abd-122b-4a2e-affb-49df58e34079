-- Police Complaint Management System Database Schema
-- Created for Chad National Police

-- Main complaints table
CREATE TABLE IF NOT EXISTS complaints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    complaint_number TEXT UNIQUE NOT NULL,
    date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Complainant Information
    complainant_name TEXT NOT NULL,
    complainant_address TEXT,
    complainant_phone TEXT,
    complainant_id_number TEXT,
    
    -- Complaint Details
    complaint_type_id INTEGER NOT NULL,
    description_ar TEXT,
    description_fr TEXT,
    location_incident TEXT NOT NULL,
    date_incident DATE NOT NULL,
    
    -- Status and Priority
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- Additional Information
    evidence_notes TEXT,
    officer_notes TEXT,
    officer_in_charge TEXT NOT NULL,
    
    -- Timestamps
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOR<PERSON><PERSON><PERSON> KEY (complaint_type_id) REFERENCES complaint_types(id)
);

-- Complaint types table
CREATE TABLE IF NOT EXISTS complaint_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name_fr TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    color_code TEXT DEFAULT '#3b82f6',
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Status history for tracking changes
CREATE TABLE IF NOT EXISTS status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    complaint_id INTEGER NOT NULL,
    old_status TEXT,
    new_status TEXT NOT NULL,
    changed_by TEXT,
    notes TEXT,
    changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (complaint_id) REFERENCES complaints(id) ON DELETE CASCADE
);

-- Insert default complaint types
INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES
(1, 'Vol', 'سرقة', '#ef4444'),
(2, 'Agression', 'اعتداء', '#f97316'),
(3, 'Fraude', 'احتيال', '#eab308'),
(4, 'Harcèlement', 'مضايقة', '#8b5cf6'),
(5, 'Dispute', 'نزاع', '#06b6d4'),
(6, 'Vandalisme', 'تخريب', '#84cc16'),
(7, 'Autre', 'أخرى', '#6b7280');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_complaints_date_registered ON complaints(date_registered);
CREATE INDEX IF NOT EXISTS idx_complaints_status ON complaints(status);
CREATE INDEX IF NOT EXISTS idx_complaints_type ON complaints(complaint_type_id);
CREATE INDEX IF NOT EXISTS idx_complaints_number ON complaints(complaint_number);
CREATE INDEX IF NOT EXISTS idx_status_history_complaint ON status_history(complaint_id);

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_complaints_timestamp 
    AFTER UPDATE ON complaints
    FOR EACH ROW
BEGIN
    UPDATE complaints SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Trigger to log status changes
CREATE TRIGGER IF NOT EXISTS log_status_change
    AFTER UPDATE OF status ON complaints
    FOR EACH ROW
    WHEN OLD.status != NEW.status
BEGIN
    INSERT INTO status_history (complaint_id, old_status, new_status, changed_by, notes)
    VALUES (NEW.id, OLD.status, NEW.status, 'System', 'Status changed from ' || OLD.status || ' to ' || NEW.status);
END;
