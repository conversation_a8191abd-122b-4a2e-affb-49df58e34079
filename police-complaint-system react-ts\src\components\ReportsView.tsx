import React, { useState, useEffect } from 'react'
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter,
  Users,
  BarChart3,
  Printer,
  Search,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader
} from 'lucide-react'
import { pdfGenerator } from '../services/pdfGenerator'

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

interface ComplaintType {
  id: number
  name_fr: string
  name_ar: string
  color_code: string
}

const ReportsView: React.FC = () => {
  const [complaints, setComplaints] = useState<Complaint[]>([])
  const [complaintTypes, setComplaintTypes] = useState<ComplaintType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedComplaint, setSelectedComplaint] = useState('')
  
  // Filter states for summary reports
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [officerFilter, setOfficerFilter] = useState('')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [complaintsData, typesData] = await Promise.all([
        window.electronAPI.getComplaints(),
        window.electronAPI.getComplaintTypes()
      ])
      
      setComplaints(complaintsData)
      setComplaintTypes(typesData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const generateIndividualReport = async () => {
    if (!selectedComplaint) {
      alert('Veuillez sélectionner une plainte')
      return
    }

    const complaint = complaints.find(c => c.id.toString() === selectedComplaint)
    if (!complaint) {
      alert('Plainte non trouvée')
      return
    }

    try {
      setIsLoading(true)
      await pdfGenerator.generateComplaintReport(complaint)
    } catch (error) {
      console.error('Error generating report:', error)
      alert('Erreur lors de la génération du rapport')
    } finally {
      setIsLoading(false)
    }
  }

  const generateSummaryReport = async () => {
    try {
      setIsLoading(true)
      
      // Apply filters
      let filteredComplaints = [...complaints]
      
      if (dateFrom) {
        filteredComplaints = filteredComplaints.filter(c => 
          new Date(c.date_registered) >= new Date(dateFrom)
        )
      }
      
      if (dateTo) {
        filteredComplaints = filteredComplaints.filter(c => 
          new Date(c.date_registered) <= new Date(dateTo)
        )
      }
      
      if (statusFilter) {
        filteredComplaints = filteredComplaints.filter(c => c.status === statusFilter)
      }
      
      if (typeFilter) {
        filteredComplaints = filteredComplaints.filter(c => 
          c.complaint_type_id === parseInt(typeFilter)
        )
      }
      
      if (officerFilter) {
        filteredComplaints = filteredComplaints.filter(c => 
          c.officer_in_charge.toLowerCase().includes(officerFilter.toLowerCase())
        )
      }

      if (filteredComplaints.length === 0) {
        alert('Aucune plainte ne correspond aux critères sélectionnés')
        return
      }

      await pdfGenerator.generateSummaryReport(filteredComplaints, 'RAPPORT RÉCAPITULATIF DES PLAINTES')
    } catch (error) {
      console.error('Error generating summary report:', error)
      alert('Erreur lors de la génération du rapport récapitulatif')
    } finally {
      setIsLoading(false)
    }
  }

  const generateStatisticsReport = async () => {
    try {
      setIsLoading(true)
      await pdfGenerator.generateSummaryReport(complaints, 'RAPPORT STATISTIQUE DES PLAINTES')
    } catch (error) {
      console.error('Error generating statistics report:', error)
      alert('Erreur lors de la génération du rapport statistique')
    } finally {
      setIsLoading(false)
    }
  }

  const getFilteredCount = () => {
    let count = complaints.length
    
    if (dateFrom) {
      count = complaints.filter(c => new Date(c.date_registered) >= new Date(dateFrom)).length
    }
    if (dateTo) {
      count = complaints.filter(c => new Date(c.date_registered) <= new Date(dateTo)).length
    }
    if (statusFilter) {
      count = complaints.filter(c => c.status === statusFilter).length
    }
    if (typeFilter) {
      count = complaints.filter(c => c.complaint_type_id === parseInt(typeFilter)).length
    }
    
    return count
  }

  const getUniqueOfficers = () => {
    return [...new Set(complaints.map(c => c.officer_in_charge))].sort()
  }

  if (isLoading && complaints.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Chargement des données...</span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Rapports</h2>
        <p className="text-sm text-gray-600">التقارير</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Individual Complaint Report */}
        <div className="card">
          <div className="flex items-center mb-4">
            <FileText className="w-6 h-6 text-blue-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Rapport Individuel</h3>
              <p className="text-sm text-gray-600">تقرير فردي</p>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">
            Générer un rapport détaillé pour une plainte spécifique avec toutes les informations.
          </p>
          
          <div className="mb-4">
            <label className="form-label">Sélectionner une Plainte</label>
            <select
              className="form-input"
              value={selectedComplaint}
              onChange={(e) => setSelectedComplaint(e.target.value)}
            >
              <option value="">Choisir une plainte...</option>
              {complaints.map((complaint) => (
                <option key={complaint.id} value={complaint.id}>
                  {complaint.complaint_number} - {complaint.complainant_name}
                </option>
              ))}
            </select>
          </div>
          
          <button
            onClick={generateIndividualReport}
            disabled={!selectedComplaint || isLoading}
            className="btn-primary w-full flex items-center justify-center"
          >
            {isLoading ? (
              <Loader className="w-4 h-4 animate-spin mr-2" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Générer le Rapport PDF
          </button>
        </div>

        {/* Summary Report */}
        <div className="card">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-6 h-6 text-green-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Rapport Récapitulatif</h3>
              <p className="text-sm text-gray-600">تقرير ملخص</p>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-4">
            Générer un rapport récapitulatif avec filtres personnalisés et statistiques.
          </p>
          
          <div className="space-y-4 mb-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="form-label">Date de début</label>
                <input
                  type="date"
                  className="form-input"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>
              <div>
                <label className="form-label">Date de fin</label>
                <input
                  type="date"
                  className="form-input"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <label className="form-label">Statut</label>
              <select
                className="form-input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">Tous les statuts</option>
                <option value="pending">En Attente</option>
                <option value="investigating">En Investigation</option>
                <option value="resolved">Résolue</option>
                <option value="closed">Fermée</option>
              </select>
            </div>
            
            <div>
              <label className="form-label">Type de Plainte</label>
              <select
                className="form-input"
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <option value="">Tous les types</option>
                {complaintTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.name_fr}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="form-label">Officier</label>
              <select
                className="form-input"
                value={officerFilter}
                onChange={(e) => setOfficerFilter(e.target.value)}
              >
                <option value="">Tous les officiers</option>
                {getUniqueOfficers().map((officer) => (
                  <option key={officer} value={officer}>
                    {officer}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-md mb-4">
            <p className="text-sm text-gray-600">
              <strong>{getFilteredCount()}</strong> plainte(s) seront incluses dans le rapport
            </p>
          </div>
          
          <button
            onClick={generateSummaryReport}
            disabled={isLoading}
            className="btn-primary w-full flex items-center justify-center"
          >
            {isLoading ? (
              <Loader className="w-4 h-4 animate-spin mr-2" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Générer le Rapport Récapitulatif
          </button>
        </div>
      </div>

      {/* Quick Reports Section */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Rapports Rapides / تقارير سريعة</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="card">
            <div className="flex items-center mb-3">
              <BarChart3 className="w-5 h-5 text-blue-600 mr-2" />
              <h4 className="font-medium text-gray-900">Statistiques Générales</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Rapport complet avec toutes les statistiques des plaintes.
            </p>
            <button
              onClick={generateStatisticsReport}
              disabled={isLoading}
              className="btn-secondary w-full text-sm flex items-center justify-center"
            >
              <Printer className="w-4 h-4 mr-2" />
              Générer
            </button>
          </div>

          <div className="card">
            <div className="flex items-center mb-3">
              <Clock className="w-5 h-5 text-yellow-600 mr-2" />
              <h4 className="font-medium text-gray-900">Plaintes en Attente</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Rapport des plaintes en attente de traitement.
            </p>
            <button
              onClick={() => {
                setStatusFilter('pending')
                setTimeout(generateSummaryReport, 100)
              }}
              disabled={isLoading}
              className="btn-secondary w-full text-sm flex items-center justify-center"
            >
              <Printer className="w-4 h-4 mr-2" />
              Générer
            </button>
          </div>

          <div className="card">
            <div className="flex items-center mb-3">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Plaintes Résolues</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Rapport des plaintes résolues ce mois.
            </p>
            <button
              onClick={() => {
                setStatusFilter('resolved')
                const firstDayOfMonth = new Date()
                firstDayOfMonth.setDate(1)
                setDateFrom(firstDayOfMonth.toISOString().split('T')[0])
                setTimeout(generateSummaryReport, 100)
              }}
              disabled={isLoading}
              className="btn-secondary w-full text-sm flex items-center justify-center"
            >
              <Printer className="w-4 h-4 mr-2" />
              Générer
            </button>
          </div>
        </div>
      </div>

      {/* Statistics Overview */}
      <div className="mt-8 card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Aperçu des Statistiques / نظرة عامة على الإحصائيات</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {complaints.length}
            </div>
            <div className="text-sm text-gray-600">Total des Plaintes</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {complaints.filter(c => c.status === 'pending').length}
            </div>
            <div className="text-sm text-gray-600">En Attente</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {complaints.filter(c => c.status === 'investigating').length}
            </div>
            <div className="text-sm text-gray-600">En Investigation</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {complaints.filter(c => c.status === 'resolved').length}
            </div>
            <div className="text-sm text-gray-600">Résolues</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReportsView
