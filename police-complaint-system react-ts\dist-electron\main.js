import { app, BrowserWindow, ipcMain } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
const require2 = createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
let db = null;
function initializeDatabase() {
  try {
    const Database = require2("better-sqlite3");
    const fs = require2("fs");
    const userDataPath = app.getPath("userData");
    const dbPath = path.join(userDataPath, "police_complaints.db");
    const dir = path.dirname(dbPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    db = new Database(dbPath);
    db.pragma("journal_mode = WAL");
    db.pragma("foreign_keys = ON");
    const schema = `
      CREATE TABLE IF NOT EXISTS complaints (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        complaint_number TEXT UNIQUE NOT NULL,
        date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
        complainant_name TEXT NOT NULL,
        complainant_address TEXT,
        complainant_phone TEXT,
        complainant_id_number TEXT,
        complaint_type_id INTEGER NOT NULL,
        description_ar TEXT,
        description_fr TEXT,
        location_incident TEXT NOT NULL,
        date_incident DATE NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
        priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        evidence_notes TEXT,
        officer_notes TEXT,
        officer_in_charge TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS complaint_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name_fr TEXT NOT NULL,
        name_ar TEXT NOT NULL,
        color_code TEXT DEFAULT '#3b82f6',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES
      (1, 'Vol', 'سرقة', '#ef4444'),
      (2, 'Agression', 'اعتداء', '#f97316'),
      (3, 'Fraude', 'احتيال', '#eab308'),
      (4, 'Harcèlement', 'مضايقة', '#8b5cf6'),
      (5, 'Dispute', 'نزاع', '#06b6d4'),
      (6, 'Vandalisme', 'تخريب', '#84cc16'),
      (7, 'Autre', 'أخرى', '#6b7280');
    `;
    const statements = schema.split(";").filter((stmt) => stmt.trim());
    for (const statement of statements) {
      if (statement.trim()) {
        db.exec(statement);
      }
    }
    console.log("Database initialized successfully");
  } catch (error) {
    console.error("Failed to initialize database:", error);
    throw error;
  }
}
function generateComplaintNumber() {
  const now = /* @__PURE__ */ new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const today = `${year}-${month}-${day}`;
  const count = db == null ? void 0 : db.prepare(`
    SELECT COUNT(*) as count
    FROM complaints
    WHERE DATE(date_registered) = DATE(?)
  `).get(today);
  const sequence = String(((count == null ? void 0 : count.count) || 0) + 1).padStart(3, "0");
  return `PL${year}${month}${day}${sequence}`;
}
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs")
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.whenReady().then(() => {
  try {
    initializeDatabase();
  } catch (error) {
    console.error("Failed to initialize database:", error);
  }
  createWindow();
  setupIpcHandlers();
});
function setupIpcHandlers() {
  ipcMain.handle("db:getComplaintTypes", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare("SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr");
      return stmt.all();
    } catch (error) {
      console.error("Error getting complaint types:", error);
      throw error;
    }
  });
  ipcMain.handle("db:createComplaint", async (event, complaint) => {
    try {
      if (!db) throw new Error("Database not initialized");
      if (!complaint.complaint_number) {
        complaint.complaint_number = generateComplaintNumber();
      }
      const stmt = db.prepare(`
        INSERT INTO complaints (
          complaint_number, complainant_name, complainant_address, complainant_phone,
          complainant_id_number, complaint_type_id, description_ar, description_fr,
          location_incident, date_incident, status, priority, evidence_notes,
          officer_notes, officer_in_charge
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        complaint.complaint_number,
        complaint.complainant_name,
        complaint.complainant_address,
        complaint.complainant_phone,
        complaint.complainant_id_number,
        complaint.complaint_type_id,
        complaint.description_ar,
        complaint.description_fr,
        complaint.location_incident,
        complaint.date_incident,
        complaint.status,
        complaint.priority,
        complaint.evidence_notes,
        complaint.officer_notes,
        complaint.officer_in_charge
      );
      return { id: result.lastInsertRowid, ...complaint };
    } catch (error) {
      console.error("Error creating complaint:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaints", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        ORDER BY c.date_registered DESC
      `);
      return stmt.all();
    } catch (error) {
      console.error("Error getting complaints:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaintById", async (event, id) => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        WHERE c.id = ?
      `);
      return stmt.get(id);
    } catch (error) {
      console.error("Error getting complaint by ID:", error);
      throw error;
    }
  });
  ipcMain.handle("db:updateComplaint", async (event, id, updates) => {
    try {
      if (!db) throw new Error("Database not initialized");
      const fields = Object.keys(updates).map((key) => `${key} = ?`).join(", ");
      const values = Object.values(updates);
      const stmt = db.prepare(`UPDATE complaints SET ${fields} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0;
    } catch (error) {
      console.error("Error updating complaint:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaintStats", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const statusStats = db.prepare(`
        SELECT status, COUNT(*) as count
        FROM complaints
        GROUP BY status
      `).all();
      const typeStats = db.prepare(`
        SELECT ct.name_fr, COUNT(*) as count
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        GROUP BY c.complaint_type_id, ct.name_fr
      `).all();
      const monthlyStats = db.prepare(`
        SELECT
          strftime('%Y-%m', date_registered) as month,
          COUNT(*) as count
        FROM complaints
        WHERE date_registered >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', date_registered)
        ORDER BY month
      `).all();
      return {
        statusStats,
        typeStats,
        monthlyStats,
        total: db.prepare("SELECT COUNT(*) as count FROM complaints").get().count
      };
    } catch (error) {
      console.error("Error getting complaint stats:", error);
      throw error;
    }
  });
}
app.on("before-quit", () => {
  if (db) {
    db.close();
    db = null;
  }
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
