import { app as p, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as g, ipc<PERSON><PERSON> as m } from "electron";
import { createRequire as O } from "node:module";
import { fileURLToPath as S } from "node:url";
import d from "node:path";
const _ = O(import.meta.url), y = d.dirname(S(import.meta.url));
let s = null;
function N() {
  return new Promise((i, e) => {
    try {
      const t = _("sqlite3").verbose(), n = _("fs"), a = p.getPath("userData"), o = d.join(a, "police_complaints.db"), r = d.dirname(o);
      n.existsSync(r) || n.mkdirSync(r, { recursive: !0 }), s = new t.Database(o, (E) => {
        if (E) {
          console.error("Error opening database:", E), e(E);
          return;
        }
        s.run("PRAGMA foreign_keys = ON");
        const l = (u, b) => {
          s.run(u, (T) => {
            if (T) {
              console.error("Error executing statement:", T), e(T);
              return;
            }
            b();
          });
        };
        l(`CREATE TABLE IF NOT EXISTS complaint_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name_fr TEXT NOT NULL,
          name_ar TEXT NOT NULL,
          color_code TEXT DEFAULT '#3b82f6',
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`, () => {
          l(`CREATE TABLE IF NOT EXISTS complaints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            complaint_number TEXT UNIQUE NOT NULL,
            date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
            complainant_name TEXT NOT NULL,
            complainant_address TEXT,
            complainant_phone TEXT,
            complainant_id_number TEXT,
            complaint_type_id INTEGER NOT NULL,
            description_ar TEXT,
            description_fr TEXT,
            location_incident TEXT NOT NULL,
            date_incident DATE NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
            evidence_notes TEXT,
            officer_notes TEXT,
            officer_in_charge TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )`, () => {
            l(`INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES
            (1, 'Vol', 'سرقة', '#ef4444'),
            (2, 'Agression', 'اعتداء', '#f97316'),
            (3, 'Fraude', 'احتيال', '#eab308'),
            (4, 'Harcèlement', 'مضايقة', '#8b5cf6'),
            (5, 'Dispute', 'نزاع', '#06b6d4'),
            (6, 'Vandalisme', 'تخريب', '#84cc16'),
            (7, 'Autre', 'أخرى', '#6b7280')`, () => {
              console.log("Database initialized successfully"), i(!0);
            });
          });
        });
      });
    } catch (t) {
      console.error("Failed to initialize database:", t), e(t);
    }
  });
}
function L() {
  return new Promise((i, e) => {
    const t = /* @__PURE__ */ new Date(), n = t.getFullYear(), a = String(t.getMonth() + 1).padStart(2, "0"), o = String(t.getDate()).padStart(2, "0"), r = `${n}-${a}-${o}`;
    s.get(`
      SELECT COUNT(*) as count
      FROM complaints
      WHERE DATE(date_registered) = DATE(?)
    `, [r], (E, l) => {
      if (E) {
        e(E);
        return;
      }
      const u = String(((l == null ? void 0 : l.count) || 0) + 1).padStart(3, "0");
      i(`PL${n}${a}${o}${u}`);
    });
  });
}
process.env.APP_ROOT = d.join(y, "..");
const f = process.env.VITE_DEV_SERVER_URL, P = d.join(process.env.APP_ROOT, "dist-electron"), h = d.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = f ? d.join(process.env.APP_ROOT, "public") : h;
let c;
function R() {
  c = new g({
    title: "Police Complaint System - République du Tchad",
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 600,
    icon: d.join(process.env.VITE_PUBLIC, "police-logo.svg"),
    webPreferences: {
      preload: d.join(y, "preload.mjs"),
      nodeIntegration: !1,
      contextIsolation: !0,
      webSecurity: !0,
      allowRunningInsecureContent: !1,
      experimentalFeatures: !1
    },
    show: !1,
    // Don't show until ready
    autoHideMenuBar: !0
    // Hide menu bar for cleaner look
  }), c.once("ready-to-show", () => {
    c == null || c.show(), c && c.focus();
  }), c.webContents.on("will-navigate", (i, e) => {
    const t = new URL(e);
    t.origin !== "http://localhost:5173" && t.origin !== "file://" && i.preventDefault();
  }), c.webContents.setWindowOpenHandler(() => ({ action: "deny" })), c.webContents.on("did-finish-load", () => {
    c == null || c.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), f ? (c.loadURL(f), c.webContents.openDevTools()) : c.loadFile(d.join(h, "index.html"));
}
p.on("window-all-closed", () => {
  process.platform !== "darwin" && (p.quit(), c = null);
});
p.on("activate", () => {
  g.getAllWindows().length === 0 && R();
});
p.commandLine.appendSwitch("disable-background-timer-throttling");
p.commandLine.appendSwitch("disable-renderer-backgrounding");
p.commandLine.appendSwitch("disable-backgrounding-occluded-windows");
p.on("web-contents-created", (i, e) => {
  e.setWindowOpenHandler(() => ({ action: "deny" }));
});
p.whenReady().then(async () => {
  try {
    await N(), console.log("Database initialized successfully");
  } catch (i) {
    console.error("Failed to initialize database:", i);
    const { dialog: e } = _("electron");
    e.showErrorBox(
      "Database Error",
      "Failed to initialize the database. The application may not function properly."
    );
  }
  R(), w(), D();
});
function D() {
  const i = _("fs"), e = _("os"), t = d.join(e.homedir(), "Documents", "PoliceReports"), n = d.join(t, "Reports"), a = d.join(t, "Backups");
  try {
    i.existsSync(t) || i.mkdirSync(t, { recursive: !0 }), i.existsSync(n) || i.mkdirSync(n, { recursive: !0 }), i.existsSync(a) || i.mkdirSync(a, { recursive: !0 }), console.log("Default directories created successfully");
  } catch (o) {
    console.error("Failed to create default directories:", o);
  }
}
function w() {
  m.handle("db:getComplaintTypes", async () => new Promise((i, e) => {
    try {
      if (!s) throw new Error("Database not initialized");
      s.all("SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr", (t, n) => {
        t ? (console.error("Error getting complaint types:", t), e(t)) : i(n);
      });
    } catch (t) {
      console.error("Error getting complaint types:", t), e(t);
    }
  })), m.handle("db:createComplaint", async (i, e) => new Promise(async (t, n) => {
    try {
      if (!s) throw new Error("Database not initialized");
      e.complaint_number || (e.complaint_number = await L());
      const a = `
          INSERT INTO complaints (
            complaint_number, complainant_name, complainant_address, complainant_phone,
            complainant_id_number, complaint_type_id, description_ar, description_fr,
            location_incident, date_incident, status, priority, evidence_notes,
            officer_notes, officer_in_charge
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, o = [
        e.complaint_number,
        e.complainant_name,
        e.complainant_address,
        e.complainant_phone,
        e.complainant_id_number,
        e.complaint_type_id,
        e.description_ar,
        e.description_fr,
        e.location_incident,
        e.date_incident,
        e.status,
        e.priority,
        e.evidence_notes,
        e.officer_notes,
        e.officer_in_charge
      ];
      s.run(a, o, function(r) {
        r ? (console.error("Error creating complaint:", r), n(r)) : t({ id: this.lastID, ...e });
      });
    } catch (a) {
      console.error("Error creating complaint:", a), n(a);
    }
  })), m.handle("db:getComplaints", async () => new Promise((i, e) => {
    try {
      if (!s) throw new Error("Database not initialized");
      s.all(`
          SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          ORDER BY c.date_registered DESC
        `, (n, a) => {
        n ? (console.error("Error getting complaints:", n), e(n)) : i(a);
      });
    } catch (t) {
      console.error("Error getting complaints:", t), e(t);
    }
  })), m.handle("db:getComplaintById", async (i, e) => new Promise((t, n) => {
    try {
      if (!s) throw new Error("Database not initialized");
      s.get(`
          SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          WHERE c.id = ?
        `, [e], (o, r) => {
        o ? (console.error("Error getting complaint by ID:", o), n(o)) : t(r);
      });
    } catch (a) {
      console.error("Error getting complaint by ID:", a), n(a);
    }
  })), m.handle("db:updateComplaint", async (i, e, t) => new Promise((n, a) => {
    try {
      if (!s) throw new Error("Database not initialized");
      const o = Object.keys(t).map((l) => `${l} = ?`).join(", "), r = [...Object.values(t), e], E = `UPDATE complaints SET ${o} WHERE id = ?`;
      s.run(E, r, function(l) {
        l ? (console.error("Error updating complaint:", l), a(l)) : n(this.changes > 0);
      });
    } catch (o) {
      console.error("Error updating complaint:", o), a(o);
    }
  })), m.handle("db:getComplaintStats", async () => new Promise((i, e) => {
    try {
      if (!s) throw new Error("Database not initialized");
      let t = { statusStats: [], typeStats: [], monthlyStats: [], total: 0 }, n = 0;
      const a = 4;
      s.all(`
          SELECT status, COUNT(*) as count
          FROM complaints
          GROUP BY status
        `, (o, r) => {
        if (o) {
          e(o);
          return;
        }
        t.statusStats = r, n++, n === a && i(t);
      }), s.all(`
          SELECT ct.name_fr, COUNT(*) as count
          FROM complaints c
          LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
          GROUP BY c.complaint_type_id, ct.name_fr
        `, (o, r) => {
        if (o) {
          e(o);
          return;
        }
        t.typeStats = r, n++, n === a && i(t);
      }), s.all(`
          SELECT
            strftime('%Y-%m', date_registered) as month,
            COUNT(*) as count
          FROM complaints
          WHERE date_registered >= date('now', '-12 months')
          GROUP BY strftime('%Y-%m', date_registered)
          ORDER BY month
        `, (o, r) => {
        if (o) {
          e(o);
          return;
        }
        t.monthlyStats = r, n++, n === a && i(t);
      }), s.get("SELECT COUNT(*) as count FROM complaints", (o, r) => {
        if (o) {
          e(o);
          return;
        }
        t.total = (r == null ? void 0 : r.count) || 0, n++, n === a && i(t);
      });
    } catch (t) {
      console.error("Error getting complaint stats:", t), e(t);
    }
  }));
}
p.on("before-quit", () => {
  s && (s.close(), s = null);
});
export {
  P as MAIN_DIST,
  h as RENDERER_DIST,
  f as VITE_DEV_SERVER_URL
};
