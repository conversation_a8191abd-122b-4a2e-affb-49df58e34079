import { app, <PERSON><PERSON>erWindow, ipc<PERSON>ain } from "electron";
import { createRequire } from "node:module";
import { fileURLToPath } from "node:url";
import path from "node:path";
const require2 = createRequire(import.meta.url);
const __dirname = path.dirname(fileURLToPath(import.meta.url));
let db = null;
function initializeDatabase() {
  return new Promise(async (resolve, reject) => {
    try {
      const initSqlJs = require2("sql.js");
      const fs = require2("fs");
      const userDataPath = app.getPath("userData");
      const dbPath = path.join(userDataPath, "police_complaints.db");
      console.log("Database path:", dbPath);
      const dir = path.dirname(dbPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log("Created directory:", dir);
      }
      const SQL = await initSqlJs();
      let data = null;
      if (fs.existsSync(dbPath)) {
        data = fs.readFileSync(dbPath);
      }
      db = new SQL.Database(data);
      db.run("PRAGMA foreign_keys = ON");
      db.run(`
        CREATE TABLE IF NOT EXISTS complaint_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name_fr TEXT NOT NULL,
          name_ar TEXT NOT NULL,
          color_code TEXT NOT NULL DEFAULT '#3b82f6',
          is_active INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      db.run(`
        CREATE TABLE IF NOT EXISTS complaints (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          complaint_number TEXT UNIQUE NOT NULL,
          date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
          complainant_name TEXT NOT NULL,
          complainant_address TEXT,
          complainant_phone TEXT,
          complainant_id_number TEXT,
          complaint_type_id INTEGER NOT NULL,
          description_ar TEXT,
          description_fr TEXT,
          location_incident TEXT NOT NULL,
          date_incident DATE NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          evidence_notes TEXT,
          officer_notes TEXT,
          officer_in_charge TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (complaint_type_id) REFERENCES complaint_types (id)
        )
      `);
      const defaultTypes = [
        [1, "Vol", "سرقة", "#ef4444"],
        [2, "Agression", "اعتداء", "#f97316"],
        [3, "Fraude", "احتيال", "#eab308"],
        [4, "Harcèlement", "مضايقة", "#8b5cf6"],
        [5, "Dispute", "نزاع", "#06b6d4"],
        [6, "Vandalisme", "تخريب", "#84cc16"],
        [7, "Autre", "أخرى", "#6b7280"]
      ];
      for (const type of defaultTypes) {
        db.run(`INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES (?, ?, ?, ?)`, type);
      }
      saveDatabase();
      console.log("Database initialized successfully with sql.js");
      resolve(true);
    } catch (error) {
      console.error("Failed to initialize database:", error);
      reject(error);
    }
  });
}
function saveDatabase() {
  if (!db) return;
  try {
    const fs = require2("fs");
    const userDataPath = app.getPath("userData");
    const dbPath = path.join(userDataPath, "police_complaints.db");
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    console.log("Database saved to:", dbPath);
  } catch (error) {
    console.error("Error saving database:", error);
  }
}
function generateComplaintNumber() {
  return new Promise((resolve, reject) => {
    try {
      if (!db) throw new Error("Database not initialized");
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const today = `${year}-${month}-${day}`;
      const stmt = db.prepare(`
        SELECT COUNT(*) as count
        FROM complaints
        WHERE DATE(date_registered) = DATE(?)
      `);
      const result = stmt.getAsObject([today]);
      const sequence = String((result.count || 0) + 1).padStart(3, "0");
      resolve(`PL${year}${month}${day}${sequence}`);
    } catch (error) {
      reject(error);
    }
  });
}
process.env.APP_ROOT = path.join(__dirname, "..");
const VITE_DEV_SERVER_URL = process.env["VITE_DEV_SERVER_URL"];
const MAIN_DIST = path.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
let win;
function createWindow() {
  win = new BrowserWindow({
    title: "Police Complaint System - République du Tchad",
    width: 1200,
    height: 800,
    minWidth: 1e3,
    minHeight: 600,
    icon: path.join(process.env.VITE_PUBLIC, "police-logo.svg"),
    webPreferences: {
      preload: path.join(__dirname, "preload.mjs"),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    show: false,
    // Don't show until ready
    autoHideMenuBar: true
    // Hide menu bar for cleaner look
  });
  win.once("ready-to-show", () => {
    win == null ? void 0 : win.show();
    if (win) {
      win.focus();
    }
  });
  win.webContents.on("will-navigate", (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    if (parsedUrl.origin !== "http://localhost:5173" && parsedUrl.origin !== "file://") {
      event.preventDefault();
    }
  });
  win.webContents.setWindowOpenHandler(() => {
    return { action: "deny" };
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (VITE_DEV_SERVER_URL) {
    win.loadURL(VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(path.join(RENDERER_DIST, "index.html"));
  }
}
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
    win = null;
  }
});
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
app.commandLine.appendSwitch("disable-background-timer-throttling");
app.commandLine.appendSwitch("disable-renderer-backgrounding");
app.commandLine.appendSwitch("disable-backgrounding-occluded-windows");
app.on("web-contents-created", (_event, contents) => {
  contents.setWindowOpenHandler(() => {
    return { action: "deny" };
  });
});
app.whenReady().then(async () => {
  try {
    await initializeDatabase();
    console.log("Database initialized successfully");
  } catch (error) {
    console.error("Failed to initialize database:", error);
    const { dialog } = require2("electron");
    dialog.showErrorBox(
      "Database Error",
      "Failed to initialize the database. The application may not function properly."
    );
  }
  createWindow();
  setupIpcHandlers();
  createDefaultDirectories();
});
function createDefaultDirectories() {
  const fs = require2("fs");
  const os = require2("os");
  const documentsPath = path.join(os.homedir(), "Documents", "PoliceReports");
  const reportsPath = path.join(documentsPath, "Reports");
  const backupsPath = path.join(documentsPath, "Backups");
  try {
    if (!fs.existsSync(documentsPath)) {
      fs.mkdirSync(documentsPath, { recursive: true });
    }
    if (!fs.existsSync(reportsPath)) {
      fs.mkdirSync(reportsPath, { recursive: true });
    }
    if (!fs.existsSync(backupsPath)) {
      fs.mkdirSync(backupsPath, { recursive: true });
    }
    console.log("Default directories created successfully");
  } catch (error) {
    console.error("Failed to create default directories:", error);
  }
}
function setupIpcHandlers() {
  ipcMain.handle("db:getComplaintTypes", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare("SELECT * FROM complaint_types WHERE is_active = 1 ORDER BY name_fr");
      const results = [];
      while (stmt.step()) {
        results.push(stmt.getAsObject());
      }
      stmt.free();
      return results;
    } catch (error) {
      console.error("Error getting complaint types:", error);
      throw error;
    }
  });
  ipcMain.handle("db:createComplaint", async (_event, complaint) => {
    try {
      if (!db) throw new Error("Database not initialized");
      if (!complaint.complaint_number) {
        complaint.complaint_number = await generateComplaintNumber();
      }
      db.run(`
        INSERT INTO complaints (
          complaint_number, complainant_name, complainant_address, complainant_phone,
          complainant_id_number, complaint_type_id, description_ar, description_fr,
          location_incident, date_incident, status, priority, evidence_notes,
          officer_notes, officer_in_charge
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        complaint.complaint_number,
        complaint.complainant_name,
        complaint.complainant_address,
        complaint.complainant_phone,
        complaint.complainant_id_number,
        complaint.complaint_type_id,
        complaint.description_ar,
        complaint.description_fr,
        complaint.location_incident,
        complaint.date_incident,
        complaint.status,
        complaint.priority,
        complaint.evidence_notes,
        complaint.officer_notes,
        complaint.officer_in_charge
      ]);
      const lastIdStmt = db.prepare("SELECT last_insert_rowid() as id");
      const result = lastIdStmt.getAsObject();
      lastIdStmt.free();
      saveDatabase();
      return { id: result.id, ...complaint };
    } catch (error) {
      console.error("Error creating complaint:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaints", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        ORDER BY c.date_registered DESC
      `);
      const results = [];
      while (stmt.step()) {
        results.push(stmt.getAsObject());
      }
      stmt.free();
      return results;
    } catch (error) {
      console.error("Error getting complaints:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaintById", async (_event, id) => {
    try {
      if (!db) throw new Error("Database not initialized");
      const stmt = db.prepare(`
        SELECT c.*, ct.name_fr as type_name_fr, ct.name_ar as type_name_ar, ct.color_code
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        WHERE c.id = ?
      `);
      const result = stmt.step() ? stmt.getAsObject([id]) : null;
      stmt.free();
      return result;
    } catch (error) {
      console.error("Error getting complaint by ID:", error);
      throw error;
    }
  });
  ipcMain.handle("db:updateComplaint", async (_event, id, updates) => {
    try {
      if (!db) throw new Error("Database not initialized");
      const fields = Object.keys(updates).map((key) => `${key} = ?`).join(", ");
      const values = [...Object.values(updates), id];
      db.run(`UPDATE complaints SET ${fields} WHERE id = ?`, values);
      saveDatabase();
      return true;
    } catch (error) {
      console.error("Error updating complaint:", error);
      throw error;
    }
  });
  ipcMain.handle("db:getComplaintStats", async () => {
    try {
      if (!db) throw new Error("Database not initialized");
      const statusStmt = db.prepare(`
        SELECT status, COUNT(*) as count
        FROM complaints
        GROUP BY status
      `);
      const statusStats = [];
      while (statusStmt.step()) {
        statusStats.push(statusStmt.getAsObject());
      }
      statusStmt.free();
      const typeStmt = db.prepare(`
        SELECT ct.name_fr, COUNT(*) as count
        FROM complaints c
        LEFT JOIN complaint_types ct ON c.complaint_type_id = ct.id
        GROUP BY c.complaint_type_id, ct.name_fr
      `);
      const typeStats = [];
      while (typeStmt.step()) {
        typeStats.push(typeStmt.getAsObject());
      }
      typeStmt.free();
      const monthlyStmt = db.prepare(`
        SELECT
          strftime('%Y-%m', date_registered) as month,
          COUNT(*) as count
        FROM complaints
        WHERE date_registered >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', date_registered)
        ORDER BY month
      `);
      const monthlyStats = [];
      while (monthlyStmt.step()) {
        monthlyStats.push(monthlyStmt.getAsObject());
      }
      monthlyStmt.free();
      const totalStmt = db.prepare("SELECT COUNT(*) as count FROM complaints");
      const totalResult = totalStmt.getAsObject();
      totalStmt.free();
      const total = totalResult.count || 0;
      return {
        statusStats,
        typeStats,
        monthlyStats,
        total
      };
    } catch (error) {
      console.error("Error getting complaint stats:", error);
      throw error;
    }
  });
}
app.on("before-quit", () => {
  if (db) {
    try {
      saveDatabase();
      db.close();
      console.log("Database saved and closed");
    } catch (error) {
      console.error("Error closing database:", error);
    }
    db = null;
  }
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
