// Police Complaint Management System - Electron Builder Configuration
{
  "$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  "appId": "com.police.complaint.system",
  "asar": true,
  "productName": "Police Complaint System",
  "copyright": "© 2024 République du Tchad - Police Nationale",
  "description": "Système de Gestion des Plaintes Policières / نظام إدارة الشكاوى الشرطية",
  "directories": {
    "output": "release/${version}"
  },
  "files": [
    "dist",
    "dist-electron",
    "public/police-logo.svg",
    "node_modules/sqlite3/**/*"
  ],
  "extraResources": [
    {
      "from": "public/police-logo.svg",
      "to": "police-logo.svg"
    }
  ],
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": [
          "x64"
        ]
      }
    ],
    "icon": "public/police-logo.svg",
    "artifactName": "Police-Complaint-System-Windows-${version}-Setup.${ext}",
    "requestedExecutionLevel": "asInvoker",
    "publisherName": "République du Tchad - Police Nationale"
  },
  "nsis": {
    "oneClick": false,
    "perMachine": false,
    "allowToChangeInstallationDirectory": true,
    "allowElevation": false,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "Police Complaint System",
    "deleteAppDataOnUninstall": false,
    "runAfterFinish": true,
    "menuCategory": "Office",
    "include": "installer.nsh",
    "installerLanguages": ["english", "french"],
    "language": "english",
    "warningsAsErrors": false,
    "displayLanguageSelector": true
  },
  "publish": null,
  "buildDependenciesFromSource": false,
  "nodeGypRebuild": false,
  "npmRebuild": true
}
