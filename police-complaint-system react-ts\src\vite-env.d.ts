/// <reference types="vite/client" />

interface ElectronAPI {
  getComplaintTypes: () => Promise<any[]>
  createComplaint: (complaint: any) => Promise<any>
  getComplaints: () => Promise<any[]>
  getComplaintById: (id: number) => Promise<any>
  updateComplaint: (id: number, updates: any) => Promise<boolean>
  getComplaintStats: () => Promise<any>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    ipcRenderer: {
      on: (channel: string, listener: (event: any, ...args: any[]) => void) => void
      off: (channel: string, listener?: (...args: any[]) => void) => void
      send: (channel: string, ...args: any[]) => void
      invoke: (channel: string, ...args: any[]) => Promise<any>
    }
  }
}
