# Police Complaint System - Custom NSIS Installer Script
# République du Tchad - Police Nationale

# Create Documents/PoliceReports folder
!macro customInstall
  # Create PoliceReports directory in user's Documents
  CreateDirectory "$DOCUMENTS\PoliceReports"
  CreateDirectory "$DOCUMENTS\PoliceReports\Backups"
  CreateDirectory "$DOCUMENTS\PoliceReports\Reports"
  
  # Set permissions for the directories
  AccessControl::GrantOnFile "$DOCUMENTS\PoliceReports" "(BU)" "FullAccess"
  AccessControl::GrantOnFile "$DOCUMENTS\PoliceReports\Backups" "(BU)" "FullAccess"
  AccessControl::GrantOnFile "$DOCUMENTS\PoliceReports\Reports" "(BU)" "FullAccess"
  
  # Create desktop shortcut with custom icon
  CreateShortCut "$DESKTOP\Police Complaint System.lnk" "$INSTDIR\Police Complaint System.exe" "" "$INSTDIR\resources\police-logo.ico" 0
  
  # Add to startup (optional - user will be prompted)
  MessageBox MB_YESNO|MB_ICONQUESTION "Voulez-vous que l'application se lance automatiquement au démarrage de Windows?$\n$\nDo you want the application to start automatically when Windows starts?" IDNO skip_startup
  CreateShortCut "$SMSTARTUP\Police Complaint System.lnk" "$INSTDIR\Police Complaint System.exe"
  skip_startup:
  
  # Write registry entries for file associations and app info
  WriteRegStr HKCU "Software\PoliceComplaintSystem" "InstallPath" "$INSTDIR"
  WriteRegStr HKCU "Software\PoliceComplaintSystem" "DataPath" "$DOCUMENTS\PoliceReports"
  WriteRegStr HKCU "Software\PoliceComplaintSystem" "Version" "${VERSION}"
  
  # Disable auto-update checks
  WriteRegDWORD HKCU "Software\PoliceComplaintSystem" "AutoUpdate" 0
  WriteRegDWORD HKCU "Software\PoliceComplaintSystem" "CheckUpdates" 0
!macroend

# Custom uninstaller
!macro customUnInstall
  # Remove desktop shortcut
  Delete "$DESKTOP\Police Complaint System.lnk"
  
  # Remove startup shortcut
  Delete "$SMSTARTUP\Police Complaint System.lnk"
  
  # Ask user if they want to keep data
  MessageBox MB_YESNO|MB_ICONQUESTION "Voulez-vous conserver vos données (plaintes et rapports)?$\n$\nDo you want to keep your data (complaints and reports)?" IDYES keep_data
  
  # Remove data directories if user chooses to
  RMDir /r "$DOCUMENTS\PoliceReports"
  
  keep_data:
  # Remove registry entries
  DeleteRegKey HKCU "Software\PoliceComplaintSystem"
!macroend

# Custom header for installer
!macro customHeader
  !define MUI_HEADERIMAGE
  !define MUI_HEADERIMAGE_BITMAP "police-header.bmp"
  !define MUI_WELCOMEFINISHPAGE_BITMAP "police-welcome.bmp"
  !define MUI_UNWELCOMEFINISHPAGE_BITMAP "police-welcome.bmp"
!macroend

# Custom installer pages
!macro customPageAfterChangeDir
  # Information page about the system
  !insertmacro MUI_PAGE_INSTFILES
  
  # Custom finish page
  !define MUI_FINISHPAGE_TITLE "Installation Terminée / Installation Complete"
  !define MUI_FINISHPAGE_TEXT "Le Système de Gestion des Plaintes Policières a été installé avec succès.$\n$\nThe Police Complaint Management System has been successfully installed.$\n$\nDossier de données: $DOCUMENTS\PoliceReports$\nData folder: $DOCUMENTS\PoliceReports"
  !define MUI_FINISHPAGE_RUN "$INSTDIR\Police Complaint System.exe"
  !define MUI_FINISHPAGE_RUN_TEXT "Lancer l'application maintenant / Launch application now"
  !insertmacro MUI_PAGE_FINISH
!macroend
