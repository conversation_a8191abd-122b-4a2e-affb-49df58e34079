{"version": 3, "file": "module-walker.js", "sourceRoot": "", "sources": ["../../src/module-walker.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,wDAA0B;AAC1B,gDAAwB;AAExB,2DAAsD;AACtD,mDAAwE;AAExE,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAIpC,MAAa,YAAY;IAUvB,YAAY,SAAiB,EAAE,eAAmC,EAAE,KAAmB,EAAE,QAAqB,EAAE,WAA4B;QAC1I,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAA,oCAAoB,EACzB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,eAAe,CACrB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,eAAe,GAAG,MAAM,IAAA,mCAAe,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAoB,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YACnD,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC;SAClE;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YACvD,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClD,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,CAAC;SACrE;QAED,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAC1B,MAAM,WAAW,GAAa,MAAM,IAAA,+BAAe,EACjD,IAAI,CAAC,SAAS,EACd,GAAG,EACH,IAAI,CAAC,eAAe,CACrB,CAAC;YACF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;aAC3D;SACF;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAE/B,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAe,EAAE,OAAuC;QAE3F,MAAM,SAAS,GAAG,MAAM,IAAA,+BAAe,EACrC,OAAO,EACP,UAAU,EACV,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YACpC,OAAO;SACR;QAED,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC3B,IAAI,gBAAgB,CAAC;QACrB,IAAI;YACF,gBAAgB,GAAG,MAAM,IAAA,mCAAe,EAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAC5D;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO;SACR;QACD,MAAM,UAAU,GAAsB,EAAE,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC,EAAE;YACnI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACtB,SAAS;aACV;YAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAE1B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC7D;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,eAAuB,EAAE,MAAM,GAAG,EAAE;QACzD,wEAAwE;QACxE,0EAA0E;QAC1E,qDAAqD;QACrD,MAAM,mBAAmB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;YACtD,OAAO;SACR;QACD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEnD,CAAC,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;QAEpC,KAAK,MAAM,UAAU,IAAI,MAAM,kBAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;YAC9D,oCAAoC;YACpC,IAAI,UAAU,KAAK,MAAM;gBAAE,SAAS;YACpC,4EAA4E;YAC5E,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;YAE9E,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACtC,SAAS;aACV;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEnC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;gBAC3G,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACtC;YAED,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC9B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;aACzD;YAED,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC,EAAE;gBAClF,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;aACrE;SACF;IACH,CAAC;CACF;AAzID,oCAyIC"}