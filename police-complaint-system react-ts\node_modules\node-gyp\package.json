{"name": "node-gyp", "description": "Node.js native addon build tool", "license": "MIT", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "gyp"], "version": "9.4.1", "installVersion": 11, "author": "<PERSON> <<EMAIL>> (http://tootallnate.net)", "repository": {"type": "git", "url": "git://github.com/nodejs/node-gyp.git"}, "preferGlobal": true, "bin": "./bin/node-gyp.js", "main": "./lib/node-gyp.js", "dependencies": {"env-paths": "^2.2.0", "exponential-backoff": "^3.1.1", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^10.0.3", "nopt": "^6.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "engines": {"node": "^12.13 || ^14.13 || >=16"}, "devDependencies": {"bindings": "^1.5.0", "mocha": "^10.2.0", "nan": "^2.14.2", "require-inject": "^1.4.4", "standard": "^14.3.4"}, "scripts": {"lint": "standard */*.js test/**/*.js", "test": "npm run lint && mocha --reporter=test/reporter.js test/test-download.js test/test-*"}}