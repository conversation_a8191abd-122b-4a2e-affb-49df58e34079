{"version": 3, "file": "PathParser.d.ts", "sourceRoot": "", "sources": ["../src/PathParser.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,wBAAwB,CAAC;AAChC,OAAO,EACN,WAAW,EACX,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,MAAM,SAAS,CAAC;AAE5B,oBAAY,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;AAC7C,oBAAY,OAAO,GAAG;IAAE,IAAI,EAAE,WAAW,CAAA;CAAE,GACxC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,GACtB,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAE1B,MAAM,CAAC,OAAO,OAAO,UAAW,SAAQ,WAAW;IAClD,OAAO,EAAE,KAAK,CAAQ;IACtB,KAAK,EAAE,KAAK,CAAQ;IACpB,OAAO,EAAE,KAAK,CAAQ;IACtB,OAAO,EAAE,OAAO,CAAQ;IACxB,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAkC;IAC9D,OAAO,CAAC,CAAC,CAAM;IACf,OAAO,CAAC,eAAe,CAAiB;IACxC,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,MAAM,CAAgB;gBAElB,IAAI,EAAE,MAAM;IAUxB,KAAK;IAWL,KAAK;IASL,IAAI;IASJ,QAAQ,CAAC,KAAK,SAAM,EAAE,KAAK,SAAM;IASjC,iBAAiB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM;IAQhD,iBAAiB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM;IAQhD,wBAAwB;IA2BxB,YAAY,CAAC,KAAK,EAAE,KAAK;IAczB,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,KAAK;IAcrD,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;IAK1C,eAAe;IAIf,eAAe;CAmBf"}