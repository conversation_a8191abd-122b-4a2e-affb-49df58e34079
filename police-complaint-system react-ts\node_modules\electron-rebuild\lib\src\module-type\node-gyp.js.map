{"version": 3, "file": "node-gyp.js", "sourceRoot": "", "sources": ["../../../src/module-type/node-gyp.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,8DAAqC;AACrC,wDAAqC;AACrC,gDAAwB;AACxB,+BAAiC;AACjC,oDAA4B;AAE5B,4CAAgD;AAChD,oDAA2D;AAC3D,wBAAiC;AAEjC,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAa,OAAQ,SAAQ,eAAY;IACvC,KAAK,CAAC,SAAS,CAAC,YAAsB;QACpC,MAAM,IAAI,GAAG;YACX,MAAM;YACN,UAAU;YACV,SAAS;YACT,GAAG,YAAY;YACf,oBAAoB;YACpB,YAAY,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;YAC5C,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YAC/B,cAAc,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxC,qBAAqB;SACtB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;SAC3D;QAED,wEAAwE;QACxE,oEAAoE;QACpE,oCAAoC;QACpC,wDAAwD;QACxD,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,oBAAoB,CAAC,IAAI,gBAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,EAAE;YAChI,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,EAAE,CAA2B,CAAC;QAC9F,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE;YAC5F,IAAI,SAAS,KAAK,eAAe,EAAE;gBACjC,OAAO;aACR;YAED,IAAI,KAAK,GAAG,WAAW,CAAA;YAEvB,IAAI,SAAS,KAAK,aAAa,EAAE;gBAC/B,KAAK,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aAC9C;YAED,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;iBAC/D,OAAO,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;iBACrG,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;iBAC9C,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;iBACtC,OAAO,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAW,CAAC;iBACtE,OAAO,CAAC,QAAQ,EAAE,MAAM,qBAAU,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC,CAAC;YAE7D,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/D,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,EAAE,YAAY,CAAC,CAAC;aACxD;YAED,OAAO,KAAK,SAAS,IAAI,KAAK,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC,CAAA;QAEH,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAa,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YACvE,OAAO,CAAC,KAAK,CAAC,2GAA2G,CAAC,CAAC;YAC3H,8DAA8D;YAC9D,0HAA0H;SAC3H;QAED,IAAI,GAAuC,CAAC;QAC5C,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,IAAI,gBAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACnC,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACzB,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,IAAA,uCAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9H,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACrC,gBAAgB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;SACrC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3D,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAE3D,MAAM,OAAO,GAAG,IAAA,kBAAa,GAAE,CAAC;QAChC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/B,OAAO,CAAC,MAAM,GAAG,4BAAgB,CAAC;QAClC,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACnC,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QACzC,IAAI;YACF,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,OAAO,OAAO,EAAE;gBACd,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;oBAChC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;iBACtF;qBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;oBACnE,oFAAoF;oBACpF,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAA2B,EAAE,EAAE;wBACjD,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,GAAW,EAAE,EAAE;4BAC5D,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC;gCAAE,OAAO,GAAG,CAAC;4BACtC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;wBACjB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAA;iBACF;gBACD,MAAM,IAAA,gBAAS,EAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9D,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;aAChC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,YAAY,GAAG,+BAA+B,IAAI,CAAC,UAAU;;;SAGhE,GAAG,CAAC,OAAO,IAAI,GAAG,MAAM,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SAC/B;gBAAS;YACR,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACnC,oEAAoE;YACpE,IAAI,CAAC,UAAU,CAAC,GAAI,CAAC,CAAC;SACvB;IACH,CAAC;IAEO,UAAU,CAAC,GAAuC;QACxD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAS,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,IAAI,GAAG,CAAS,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAEvD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACzB;iBAAM,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aAC7B;SACF;QACD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aAC7B;SACF;IACH,CAAC;CACF;AAnJD,0BAmJC"}