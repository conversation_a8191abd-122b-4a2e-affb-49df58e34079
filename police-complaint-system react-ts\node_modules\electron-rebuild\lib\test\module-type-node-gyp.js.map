{"version": 3, "file": "module-type-node-gyp.js", "sourceRoot": "", "sources": ["../../test/module-type-node-gyp.ts"], "names": [], "mappings": ";;;;;AAAA,mCAAsC;AACtC,+BAA8B;AAC9B,4CAAoB;AACpB,gDAAwB;AAExB,yDAA4E;AAC5E,0DAAsD;AACtD,4CAA2C;AAE3C,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;IACxB,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,MAAM,cAAc,GAAG,cAAI,CAAC,OAAO,CAAC,YAAE,CAAC,MAAM,EAAE,EAAE,uBAAuB,CAAC,CAAC;QAE1E,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,IAAA,8BAAe,EAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;QACjE,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,IAAA,gCAAiB,EAAC,cAAc,CAAC,CAAC,CAAC;QAE3D,SAAS,6BAA6B,CAAC,eAAuB;YAC5D,MAAM,SAAS,GAAG,IAAI,mBAAS,CAAC;gBAC9B,SAAS,EAAE,cAAc;gBACzB,eAAe,EAAE,eAAe;gBAChC,SAAS,EAAE,IAAI,qBAAY,EAAE;aAC9B,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,kBAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACvD,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAClF,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;gBACpD,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;gBAC3E,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;gBACpF,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,wCAAwC,EAAE,GAAG,EAAE;YACrD,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;gBAC/D,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;gBAC/D,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;gBACjE,MAAM,IAAI,GAAG,MAAM,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;gBACnE,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}