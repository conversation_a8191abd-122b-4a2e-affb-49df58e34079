import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Complaint, ComplaintType } from '../database/database';

interface ComplaintFilters {
  status?: string;
  priority?: string;
  type?: number;
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
}

interface ComplaintStats {
  total: number;
  pending: number;
  investigating: number;
  resolved: number;
  closed: number;
  byType: { [key: string]: number };
  byPriority: { [key: string]: number };
}

interface ComplaintStore {
  // State
  complaints: Complaint[];
  complaintTypes: ComplaintType[];
  currentComplaint: Complaint | null;
  filters: ComplaintFilters;
  stats: ComplaintStats;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setComplaints: (complaints: Complaint[]) => void;
  setComplaintTypes: (types: ComplaintType[]) => void;
  setCurrentComplaint: (complaint: Complaint | null) => void;
  setFilters: (filters: ComplaintFilters) => void;
  setStats: (stats: ComplaintStats) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed getters
  getFilteredComplaints: () => Complaint[];
  getComplaintById: (id: number) => Complaint | undefined;
  getComplaintTypeById: (id: number) => ComplaintType | undefined;
  
  // Utility actions
  addComplaint: (complaint: Complaint) => void;
  updateComplaint: (id: number, updates: Partial<Complaint>) => void;
  removeComplaint: (id: number) => void;
  clearFilters: () => void;
  refreshStats: () => void;
}

const initialStats: ComplaintStats = {
  total: 0,
  pending: 0,
  investigating: 0,
  resolved: 0,
  closed: 0,
  byType: {},
  byPriority: {}
};

export const useComplaintStore = create<ComplaintStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      complaints: [],
      complaintTypes: [],
      currentComplaint: null,
      filters: {},
      stats: initialStats,
      isLoading: false,
      error: null,
      
      // Basic setters
      setComplaints: (complaints) => set({ complaints }),
      setComplaintTypes: (types) => set({ complaintTypes: types }),
      setCurrentComplaint: (complaint) => set({ currentComplaint: complaint }),
      setFilters: (filters) => set({ filters }),
      setStats: (stats) => set({ stats }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      
      // Computed getters
      getFilteredComplaints: () => {
        const { complaints, filters } = get();
        
        return complaints.filter(complaint => {
          // Status filter
          if (filters.status && complaint.status !== filters.status) {
            return false;
          }
          
          // Priority filter
          if (filters.priority && complaint.priority !== filters.priority) {
            return false;
          }
          
          // Type filter
          if (filters.type && complaint.complaint_type_id !== filters.type) {
            return false;
          }
          
          // Date range filter
          if (filters.dateFrom) {
            const complaintDate = new Date(complaint.date_registered || '');
            const fromDate = new Date(filters.dateFrom);
            if (complaintDate < fromDate) return false;
          }
          
          if (filters.dateTo) {
            const complaintDate = new Date(complaint.date_registered || '');
            const toDate = new Date(filters.dateTo);
            if (complaintDate > toDate) return false;
          }
          
          // Search term filter
          if (filters.searchTerm) {
            const searchLower = filters.searchTerm.toLowerCase();
            const searchFields = [
              complaint.complaint_number,
              complaint.complainant_name,
              complaint.description_fr,
              complaint.description_ar,
              complaint.location_incident,
              complaint.officer_in_charge
            ];
            
            const matches = searchFields.some(field => 
              field?.toLowerCase().includes(searchLower)
            );
            
            if (!matches) return false;
          }
          
          return true;
        });
      },
      
      getComplaintById: (id) => {
        const { complaints } = get();
        return complaints.find(complaint => complaint.id === id);
      },
      
      getComplaintTypeById: (id) => {
        const { complaintTypes } = get();
        return complaintTypes.find(type => type.id === id);
      },
      
      // Utility actions
      addComplaint: (complaint) => {
        const { complaints } = get();
        set({ complaints: [...complaints, complaint] });
        get().refreshStats();
      },
      
      updateComplaint: (id, updates) => {
        const { complaints } = get();
        const updatedComplaints = complaints.map(complaint =>
          complaint.id === id ? { ...complaint, ...updates } : complaint
        );
        set({ complaints: updatedComplaints });
        get().refreshStats();
      },
      
      removeComplaint: (id) => {
        const { complaints } = get();
        const filteredComplaints = complaints.filter(complaint => complaint.id !== id);
        set({ complaints: filteredComplaints });
        get().refreshStats();
      },
      
      clearFilters: () => {
        set({ filters: {} });
      },
      
      refreshStats: () => {
        const { complaints, complaintTypes } = get();
        
        const stats: ComplaintStats = {
          total: complaints.length,
          pending: complaints.filter(c => c.status === 'pending').length,
          investigating: complaints.filter(c => c.status === 'investigating').length,
          resolved: complaints.filter(c => c.status === 'resolved').length,
          closed: complaints.filter(c => c.status === 'closed').length,
          byType: {},
          byPriority: {}
        };
        
        // Calculate stats by type
        complaintTypes.forEach(type => {
          stats.byType[type.name_fr] = complaints.filter(
            c => c.complaint_type_id === type.id
          ).length;
        });
        
        // Calculate stats by priority
        const priorities = ['low', 'medium', 'high', 'urgent'];
        priorities.forEach(priority => {
          stats.byPriority[priority] = complaints.filter(
            c => c.priority === priority
          ).length;
        });
        
        set({ stats });
      }
    }),
    {
      name: 'complaint-store',
    }
  )
);
