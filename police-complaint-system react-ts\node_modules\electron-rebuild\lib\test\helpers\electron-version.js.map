{"version": 3, "file": "electron-version.js", "sourceRoot": "", "sources": ["../../../test/helpers/electron-version.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,2CAA6B;AAE7B,SAAS,mBAAmB;IAC1B,8DAA8D;IAC9D,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IACzC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;KAC9E;SAAM;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAgB,2BAA2B;IACzC,OAAO,EAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;AAClE,CAAC;AAFD,kEAEC"}