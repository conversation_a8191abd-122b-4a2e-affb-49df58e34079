import React, { useState, useEffect } from 'react'
import {
  <PERSON>tings,
  User,
  Globe,
  Database,
  FileText,
  Shield,
  Bell,
  Palette,
  Save,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  AlertCircle,
  Info,
  Download,
  Upload,
  HardDrive,
  Clock,
  Lock,
  Eye,
  EyeOff
} from 'lucide-react'

interface ComplaintType {
  id: number
  name_fr: string
  name_ar: string
  color_code: string
  is_active: boolean
}

interface SystemSettings {
  language: 'fr' | 'ar' | 'both'
  theme: 'light' | 'dark' | 'auto'
  notifications: boolean
  autoBackup: boolean
  backupInterval: number
  maxFileSize: number
  sessionTimeout: number
  requirePasswordChange: boolean
  passwordMinLength: number
}

interface UserProfile {
  name: string
  badge_number: string
  rank: string
  department: string
  email: string
  phone: string
}

const SettingsView: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'general' | 'types' | 'security' | 'backup' | 'profile'>('general')
  const [complaintTypes, setComplaintTypes] = useState<ComplaintType[]>([])
  const [settings, setSettings] = useState<SystemSettings>({
    language: 'both',
    theme: 'light',
    notifications: true,
    autoBackup: true,
    backupInterval: 24,
    maxFileSize: 10,
    sessionTimeout: 30,
    requirePasswordChange: false,
    passwordMinLength: 8
  })
  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: 'Agent de Police',
    badge_number: 'POL001',
    rank: 'Inspecteur',
    department: 'Police Nationale',
    email: '<EMAIL>',
    phone: '+235 XX XX XX XX'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null)
  const [editingType, setEditingType] = useState<ComplaintType | null>(null)
  const [showNewTypeForm, setShowNewTypeForm] = useState(false)
  const [newType, setNewType] = useState({ name_fr: '', name_ar: '', color_code: '#3b82f6' })

  useEffect(() => {
    loadSettings()
    loadComplaintTypes()
  }, [])

  const loadSettings = async () => {
    try {
      // Load settings from localStorage or API
      const savedSettings = localStorage.getItem('policeSystemSettings')
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
      
      const savedProfile = localStorage.getItem('policeUserProfile')
      if (savedProfile) {
        setUserProfile(JSON.parse(savedProfile))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const loadComplaintTypes = async () => {
    try {
      const types = await window.electronAPI.getComplaintTypes()
      setComplaintTypes(types)
    } catch (error) {
      console.error('Error loading complaint types:', error)
    }
  }

  const saveSettings = async () => {
    try {
      setIsLoading(true)
      
      // Save to localStorage (in a real app, this would be an API call)
      localStorage.setItem('policeSystemSettings', JSON.stringify(settings))
      localStorage.setItem('policeUserProfile', JSON.stringify(userProfile))
      
      setSaveMessage({ type: 'success', message: 'Paramètres sauvegardés avec succès' })
      setTimeout(() => setSaveMessage(null), 3000)
    } catch (error) {
      setSaveMessage({ type: 'error', message: 'Erreur lors de la sauvegarde' })
      setTimeout(() => setSaveMessage(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const addComplaintType = async () => {
    try {
      if (!newType.name_fr.trim() || !newType.name_ar.trim()) {
        setSaveMessage({ type: 'error', message: 'Veuillez remplir tous les champs' })
        return
      }

      const result = await window.electronAPI.addComplaintType({
        name_fr: newType.name_fr,
        name_ar: newType.name_ar,
        color_code: newType.color_code,
        is_active: true
      })

      if (result) {
        await loadComplaintTypes()
        setNewType({ name_fr: '', name_ar: '', color_code: '#3b82f6' })
        setShowNewTypeForm(false)
        setSaveMessage({ type: 'success', message: 'Type de plainte ajouté avec succès' })
      }
    } catch (error) {
      setSaveMessage({ type: 'error', message: 'Erreur lors de l\'ajout du type' })
    }
  }

  const updateComplaintType = async (type: ComplaintType) => {
    try {
      const result = await window.electronAPI.updateComplaintType(type)
      if (result) {
        await loadComplaintTypes()
        setEditingType(null)
        setSaveMessage({ type: 'success', message: 'Type de plainte mis à jour' })
      }
    } catch (error) {
      setSaveMessage({ type: 'error', message: 'Erreur lors de la mise à jour' })
    }
  }

  const deleteComplaintType = async (id: number) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce type de plainte ?')) {
      try {
        const result = await window.electronAPI.deleteComplaintType(id)
        if (result) {
          await loadComplaintTypes()
          setSaveMessage({ type: 'success', message: 'Type de plainte supprimé' })
        }
      } catch (error) {
        setSaveMessage({ type: 'error', message: 'Erreur lors de la suppression' })
      }
    }
  }

  const exportData = async () => {
    try {
      setIsLoading(true)
      // In a real app, this would trigger a data export
      setSaveMessage({ type: 'success', message: 'Export des données initié' })
    } catch (error) {
      setSaveMessage({ type: 'error', message: 'Erreur lors de l\'export' })
    } finally {
      setIsLoading(false)
    }
  }

  const importData = async () => {
    try {
      setIsLoading(true)
      // In a real app, this would trigger a file picker and import
      setSaveMessage({ type: 'success', message: 'Import des données initié' })
    } catch (error) {
      setSaveMessage({ type: 'error', message: 'Erreur lors de l\'import' })
    } finally {
      setIsLoading(false)
    }
  }

  const tabs = [
    { id: 'general', label: 'Général', labelAr: 'عام', icon: Settings },
    { id: 'types', label: 'Types de Plaintes', labelAr: 'أنواع الشكاوى', icon: FileText },
    { id: 'security', label: 'Sécurité', labelAr: 'الأمان', icon: Shield },
    { id: 'backup', label: 'Sauvegarde', labelAr: 'النسخ الاحتياطي', icon: Database },
    { id: 'profile', label: 'Profil', labelAr: 'الملف الشخصي', icon: User }
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Paramètres</h2>
          <p className="text-sm text-gray-600">الإعدادات</p>
        </div>
        <button
          onClick={saveSettings}
          disabled={isLoading}
          className="btn-primary flex items-center"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          Sauvegarder
        </button>
      </div>

      {/* Save Message */}
      {saveMessage && (
        <div className={`mb-6 p-4 rounded-lg flex items-center ${
          saveMessage.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {saveMessage.type === 'success' ? (
            <Check className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {saveMessage.message}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  <div>
                    <div className="font-medium">{tab.label}</div>
                    <div className="text-xs text-gray-500">{tab.labelAr}</div>
                  </div>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <div className="card">
            {activeTab === 'general' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Paramètres Généraux</h3>
                
                <div className="space-y-6">
                  {/* Language Settings */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Langue / اللغة
                    </label>
                    <select
                      value={settings.language}
                      onChange={(e) => setSettings({...settings, language: e.target.value as any})}
                      className="form-input"
                    >
                      <option value="fr">Français uniquement</option>
                      <option value="ar">العربية فقط</option>
                      <option value="both">Bilingue (Français + العربية)</option>
                    </select>
                  </div>

                  {/* Theme Settings */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Thème / المظهر
                    </label>
                    <select
                      value={settings.theme}
                      onChange={(e) => setSettings({...settings, theme: e.target.value as any})}
                      className="form-input"
                    >
                      <option value="light">Clair</option>
                      <option value="dark">Sombre</option>
                      <option value="auto">Automatique</option>
                    </select>
                  </div>

                  {/* Notifications */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Notifications
                      </label>
                      <p className="text-xs text-gray-500">Recevoir des notifications système</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications}
                        onChange={(e) => setSettings({...settings, notifications: e.target.checked})}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Session Timeout */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Délai d'expiration de session (minutes)
                    </label>
                    <input
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => setSettings({...settings, sessionTimeout: parseInt(e.target.value)})}
                      className="form-input"
                      min="5"
                      max="480"
                    />
                  </div>

                  {/* Max File Size */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Taille maximale des fichiers (MB)
                    </label>
                    <input
                      type="number"
                      value={settings.maxFileSize}
                      onChange={(e) => setSettings({...settings, maxFileSize: parseInt(e.target.value)})}
                      className="form-input"
                      min="1"
                      max="100"
                    />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'types' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Types de Plaintes</h3>
                  <button
                    onClick={() => setShowNewTypeForm(true)}
                    className="btn-primary flex items-center"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Ajouter un Type
                  </button>
                </div>

                {/* New Type Form */}
                {showNewTypeForm && (
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-4">Nouveau Type de Plainte</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nom (Français)
                        </label>
                        <input
                          type="text"
                          value={newType.name_fr}
                          onChange={(e) => setNewType({...newType, name_fr: e.target.value})}
                          className="form-input"
                          placeholder="Ex: Vol"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nom (العربية)
                        </label>
                        <input
                          type="text"
                          value={newType.name_ar}
                          onChange={(e) => setNewType({...newType, name_ar: e.target.value})}
                          className="form-input text-right"
                          placeholder="مثال: سرقة"
                          dir="rtl"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Couleur
                        </label>
                        <input
                          type="color"
                          value={newType.color_code}
                          onChange={(e) => setNewType({...newType, color_code: e.target.value})}
                          className="form-input h-10"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 mt-4">
                      <button
                        onClick={addComplaintType}
                        className="btn-primary flex items-center"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Ajouter
                      </button>
                      <button
                        onClick={() => {
                          setShowNewTypeForm(false)
                          setNewType({ name_fr: '', name_ar: '', color_code: '#3b82f6' })
                        }}
                        className="btn-secondary flex items-center"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Annuler
                      </button>
                    </div>
                  </div>
                )}

                {/* Types List */}
                <div className="space-y-3">
                  {complaintTypes.map((type) => (
                    <div key={type.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      {editingType?.id === type.id ? (
                        <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                          <input
                            type="text"
                            value={editingType.name_fr}
                            onChange={(e) => setEditingType({...editingType, name_fr: e.target.value})}
                            className="form-input"
                          />
                          <input
                            type="text"
                            value={editingType.name_ar}
                            onChange={(e) => setEditingType({...editingType, name_ar: e.target.value})}
                            className="form-input text-right"
                            dir="rtl"
                          />
                          <input
                            type="color"
                            value={editingType.color_code}
                            onChange={(e) => setEditingType({...editingType, color_code: e.target.value})}
                            className="form-input h-10"
                          />
                        </div>
                      ) : (
                        <div className="flex items-center flex-1">
                          <div
                            className="w-4 h-4 rounded-full mr-3"
                            style={{ backgroundColor: type.color_code }}
                          ></div>
                          <div>
                            <p className="font-medium text-gray-900">{type.name_fr}</p>
                            <p className="text-sm text-gray-500 text-right" dir="rtl">{type.name_ar}</p>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        {editingType?.id === type.id ? (
                          <>
                            <button
                              onClick={() => updateComplaintType(editingType)}
                              className="p-2 text-green-600 hover:bg-green-100 rounded"
                            >
                              <Check className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setEditingType(null)}
                              className="p-2 text-gray-600 hover:bg-gray-100 rounded"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              onClick={() => setEditingType(type)}
                              className="p-2 text-blue-600 hover:bg-blue-100 rounded"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => deleteComplaintType(type.id)}
                              className="p-2 text-red-600 hover:bg-red-100 rounded"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Paramètres de Sécurité</h3>

                <div className="space-y-6">
                  {/* Password Requirements */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Politique des Mots de Passe</h4>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Exiger un changement de mot de passe
                          </label>
                          <p className="text-xs text-gray-500">Forcer les utilisateurs à changer leur mot de passe</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.requirePasswordChange}
                            onChange={(e) => setSettings({...settings, requirePasswordChange: e.target.checked})}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Longueur minimale du mot de passe
                        </label>
                        <input
                          type="number"
                          value={settings.passwordMinLength}
                          onChange={(e) => setSettings({...settings, passwordMinLength: parseInt(e.target.value)})}
                          className="form-input"
                          min="6"
                          max="32"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Access Control */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Contrôle d'Accès</h4>

                    <div className="space-y-4">
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center">
                          <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
                          <div>
                            <h5 className="font-medium text-yellow-800">Permissions Administrateur</h5>
                            <p className="text-sm text-yellow-700">Seuls les administrateurs peuvent modifier ces paramètres</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Lock className="w-4 h-4 text-gray-600 mr-2" />
                            <h5 className="font-medium text-gray-900">Gestion des Plaintes</h5>
                          </div>
                          <p className="text-sm text-gray-600">Créer, modifier et supprimer des plaintes</p>
                        </div>

                        <div className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center mb-2">
                            <FileText className="w-4 h-4 text-gray-600 mr-2" />
                            <h5 className="font-medium text-gray-900">Génération de Rapports</h5>
                          </div>
                          <p className="text-sm text-gray-600">Accès aux rapports et statistiques</p>
                        </div>

                        <div className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Settings className="w-4 h-4 text-gray-600 mr-2" />
                            <h5 className="font-medium text-gray-900">Administration Système</h5>
                          </div>
                          <p className="text-sm text-gray-600">Configuration et paramètres système</p>
                        </div>

                        <div className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Database className="w-4 h-4 text-gray-600 mr-2" />
                            <h5 className="font-medium text-gray-900">Gestion des Données</h5>
                          </div>
                          <p className="text-sm text-gray-600">Sauvegarde et restauration</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Audit Log */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Journal d'Audit</h4>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h5 className="font-medium text-gray-900">Activités Récentes</h5>
                          <p className="text-sm text-gray-600">Dernières actions système</p>
                        </div>
                        <button className="btn-secondary text-sm">
                          Voir Tout
                        </button>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center text-sm">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-gray-600">Connexion utilisateur - Agent POL001</span>
                          <span className="ml-auto text-gray-500">Il y a 2 heures</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                          <span className="text-gray-600">Plainte créée - PL20250619001</span>
                          <span className="ml-auto text-gray-500">Il y a 4 heures</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                          <span className="text-gray-600">Paramètres modifiés</span>
                          <span className="ml-auto text-gray-500">Hier</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'backup' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Sauvegarde et Restauration</h3>

                <div className="space-y-6">
                  {/* Auto Backup Settings */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Sauvegarde Automatique</h4>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Activer la sauvegarde automatique
                          </label>
                          <p className="text-xs text-gray-500">Sauvegarder automatiquement la base de données</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.autoBackup}
                            onChange={(e) => setSettings({...settings, autoBackup: e.target.checked})}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Intervalle de sauvegarde (heures)
                        </label>
                        <select
                          value={settings.backupInterval}
                          onChange={(e) => setSettings({...settings, backupInterval: parseInt(e.target.value)})}
                          className="form-input"
                          disabled={!settings.autoBackup}
                        >
                          <option value={1}>Chaque heure</option>
                          <option value={6}>Toutes les 6 heures</option>
                          <option value={12}>Toutes les 12 heures</option>
                          <option value={24}>Quotidienne</option>
                          <option value={168}>Hebdomadaire</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Manual Backup */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Sauvegarde Manuelle</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center mb-3">
                          <Download className="w-5 h-5 text-blue-600 mr-2" />
                          <h5 className="font-medium text-gray-900">Exporter les Données</h5>
                        </div>
                        <p className="text-sm text-gray-600 mb-4">
                          Créer une sauvegarde complète de toutes les plaintes et paramètres
                        </p>
                        <button
                          onClick={exportData}
                          disabled={isLoading}
                          className="btn-primary w-full flex items-center justify-center"
                        >
                          {isLoading ? (
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Download className="w-4 h-4 mr-2" />
                          )}
                          Exporter
                        </button>
                      </div>

                      <div className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center mb-3">
                          <Upload className="w-5 h-5 text-green-600 mr-2" />
                          <h5 className="font-medium text-gray-900">Importer les Données</h5>
                        </div>
                        <p className="text-sm text-gray-600 mb-4">
                          Restaurer les données à partir d'un fichier de sauvegarde
                        </p>
                        <button
                          onClick={importData}
                          disabled={isLoading}
                          className="btn-secondary w-full flex items-center justify-center"
                        >
                          {isLoading ? (
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Upload className="w-4 h-4 mr-2" />
                          )}
                          Importer
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Storage Information */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Informations de Stockage</h4>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center mb-2">
                          <HardDrive className="w-5 h-5 text-blue-600 mr-2" />
                          <h5 className="font-medium text-blue-900">Base de Données</h5>
                        </div>
                        <p className="text-2xl font-bold text-blue-900">2.4 MB</p>
                        <p className="text-sm text-blue-700">Taille actuelle</p>
                      </div>

                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Clock className="w-5 h-5 text-green-600 mr-2" />
                          <h5 className="font-medium text-green-900">Dernière Sauvegarde</h5>
                        </div>
                        <p className="text-lg font-bold text-green-900">Aujourd'hui</p>
                        <p className="text-sm text-green-700">14:30</p>
                      </div>

                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center mb-2">
                          <FileText className="w-5 h-5 text-yellow-600 mr-2" />
                          <h5 className="font-medium text-yellow-900">Sauvegardes</h5>
                        </div>
                        <p className="text-2xl font-bold text-yellow-900">7</p>
                        <p className="text-sm text-yellow-700">Fichiers disponibles</p>
                      </div>
                    </div>
                  </div>

                  {/* Backup History */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Historique des Sauvegardes</h4>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="space-y-3">
                        {[
                          { date: '19/06/2025 14:30', size: '2.4 MB', type: 'Automatique', status: 'Réussie' },
                          { date: '19/06/2025 02:30', size: '2.3 MB', type: 'Automatique', status: 'Réussie' },
                          { date: '18/06/2025 18:45', size: '2.2 MB', type: 'Manuelle', status: 'Réussie' },
                          { date: '18/06/2025 14:30', size: '2.2 MB', type: 'Automatique', status: 'Réussie' },
                          { date: '18/06/2025 02:30', size: '2.1 MB', type: 'Automatique', status: 'Réussie' }
                        ].map((backup, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                            <div className="flex items-center">
                              <div className={`w-3 h-3 rounded-full mr-3 ${
                                backup.status === 'Réussie' ? 'bg-green-500' : 'bg-red-500'
                              }`}></div>
                              <div>
                                <p className="text-sm font-medium text-gray-900">{backup.date}</p>
                                <p className="text-xs text-gray-500">{backup.type} - {backup.size}</p>
                              </div>
                            </div>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              backup.status === 'Réussie'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {backup.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Profil Utilisateur</h3>

                <div className="space-y-6">
                  {/* Profile Information */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Informations Personnelles</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Nom Complet
                        </label>
                        <input
                          type="text"
                          value={userProfile.name}
                          onChange={(e) => setUserProfile({...userProfile, name: e.target.value})}
                          className="form-input"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Numéro de Badge
                        </label>
                        <input
                          type="text"
                          value={userProfile.badge_number}
                          onChange={(e) => setUserProfile({...userProfile, badge_number: e.target.value})}
                          className="form-input"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Grade
                        </label>
                        <select
                          value={userProfile.rank}
                          onChange={(e) => setUserProfile({...userProfile, rank: e.target.value})}
                          className="form-input"
                        >
                          <option value="Agent">Agent</option>
                          <option value="Brigadier">Brigadier</option>
                          <option value="Inspecteur">Inspecteur</option>
                          <option value="Commissaire">Commissaire</option>
                          <option value="Commandant">Commandant</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Département
                        </label>
                        <input
                          type="text"
                          value={userProfile.department}
                          onChange={(e) => setUserProfile({...userProfile, department: e.target.value})}
                          className="form-input"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email
                        </label>
                        <input
                          type="email"
                          value={userProfile.email}
                          onChange={(e) => setUserProfile({...userProfile, email: e.target.value})}
                          className="form-input"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Téléphone
                        </label>
                        <input
                          type="tel"
                          value={userProfile.phone}
                          onChange={(e) => setUserProfile({...userProfile, phone: e.target.value})}
                          className="form-input"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Account Security */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Sécurité du Compte</h4>

                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-blue-900">Changer le Mot de Passe</h5>
                            <p className="text-sm text-blue-700">Dernière modification: Il y a 30 jours</p>
                          </div>
                          <button className="btn-primary">
                            Modifier
                          </button>
                        </div>
                      </div>

                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-green-900">Authentification à Deux Facteurs</h5>
                            <p className="text-sm text-green-700">Sécurité renforcée pour votre compte</p>
                          </div>
                          <button className="btn-secondary">
                            Configurer
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Activity Summary */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Résumé d'Activité</h4>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <FileText className="w-5 h-5 text-gray-600 mr-2" />
                          <h5 className="font-medium text-gray-900">Plaintes Traitées</h5>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">24</p>
                        <p className="text-sm text-gray-600">Ce mois</p>
                      </div>

                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Clock className="w-5 h-5 text-gray-600 mr-2" />
                          <h5 className="font-medium text-gray-900">Temps Moyen</h5>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">4.2</p>
                        <p className="text-sm text-gray-600">Jours par plainte</p>
                      </div>

                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Check className="w-5 h-5 text-gray-600 mr-2" />
                          <h5 className="font-medium text-gray-900">Taux de Résolution</h5>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">87%</p>
                        <p className="text-sm text-gray-600">Performance</p>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Activité Récente</h4>

                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="space-y-3">
                        {[
                          { action: 'Plainte résolue', details: 'PL20250619003 - Vol de véhicule', time: 'Il y a 2 heures' },
                          { action: 'Rapport généré', details: 'Rapport mensuel des statistiques', time: 'Il y a 4 heures' },
                          { action: 'Plainte créée', details: 'PL20250619002 - Agression', time: 'Il y a 6 heures' },
                          { action: 'Mise à jour profil', details: 'Informations de contact modifiées', time: 'Hier' }
                        ].map((activity, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                            <div>
                              <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                              <p className="text-xs text-gray-500">{activity.details}</p>
                            </div>
                            <span className="text-xs text-gray-500">{activity.time}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsView
