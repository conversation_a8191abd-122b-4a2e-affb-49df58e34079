import React, { useState, useEffect } from 'react'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Calendar,
  User,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader
} from 'lucide-react'
import ComplaintDetailModal from './ComplaintDetailModal'

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

interface ComplaintType {
  id: number
  name_fr: string
  name_ar: string
  color_code: string
}

const AllComplaintsView: React.FC = () => {
  const [complaints, setComplaints] = useState<Complaint[]>([])
  const [complaintTypes, setComplaintTypes] = useState<ComplaintType[]>([])
  const [filteredComplaints, setFilteredComplaints] = useState<Complaint[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedComplaint, setSelectedComplaint] = useState<Complaint | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [dateFromFilter, setDateFromFilter] = useState('')
  const [dateToFilter, setDateToFilter] = useState('')

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [complaints, searchTerm, statusFilter, priorityFilter, typeFilter, dateFromFilter, dateToFilter])

  const loadData = async () => {
    try {
      setIsLoading(true)
      const [complaintsData, typesData] = await Promise.all([
        window.electronAPI.getComplaints(),
        window.electronAPI.getComplaintTypes()
      ])
      
      setComplaints(complaintsData)
      setComplaintTypes(typesData)
    } catch (error) {
      console.error('Error loading complaints:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...complaints]

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(complaint =>
        complaint.complaint_number.toLowerCase().includes(searchLower) ||
        complaint.complainant_name.toLowerCase().includes(searchLower) ||
        complaint.location_incident.toLowerCase().includes(searchLower) ||
        complaint.officer_in_charge.toLowerCase().includes(searchLower) ||
        complaint.description_fr?.toLowerCase().includes(searchLower) ||
        complaint.description_ar?.includes(searchTerm)
      )
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(complaint => complaint.status === statusFilter)
    }

    // Priority filter
    if (priorityFilter) {
      filtered = filtered.filter(complaint => complaint.priority === priorityFilter)
    }

    // Type filter
    if (typeFilter) {
      filtered = filtered.filter(complaint => complaint.complaint_type_id === parseInt(typeFilter))
    }

    // Date range filter
    if (dateFromFilter) {
      filtered = filtered.filter(complaint => 
        new Date(complaint.date_registered) >= new Date(dateFromFilter)
      )
    }

    if (dateToFilter) {
      filtered = filtered.filter(complaint => 
        new Date(complaint.date_registered) <= new Date(dateToFilter)
      )
    }

    setFilteredComplaints(filtered)
  }

  const clearFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
    setPriorityFilter('')
    setTypeFilter('')
    setDateFromFilter('')
    setDateToFilter('')
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'En Attente', labelAr: 'في الانتظار', class: 'status-pending', icon: Clock },
      investigating: { label: 'En Investigation', labelAr: 'قيد التحقيق', class: 'status-investigating', icon: Search },
      resolved: { label: 'Résolue', labelAr: 'محلولة', class: 'status-resolved', icon: CheckCircle },
      closed: { label: 'Fermée', labelAr: 'مغلقة', class: 'status-closed', icon: XCircle }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return null
    
    const Icon = config.icon
    return (
      <span className={`${config.class} flex items-center`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </span>
    )
  }

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'Faible', class: 'priority-low' },
      medium: { label: 'Moyen', class: 'priority-medium' },
      high: { label: 'Élevé', class: 'priority-high' },
      urgent: { label: 'Urgent', class: 'priority-urgent' }
    }
    
    const config = priorityConfig[priority as keyof typeof priorityConfig]
    if (!config) return null
    
    return <span className={config.class}>{config.label}</span>
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const viewComplaintDetails = (complaint: Complaint) => {
    setSelectedComplaint(complaint)
    setShowDetailModal(true)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Chargement des plaintes...</span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Toutes les Plaintes</h2>
        <p className="text-sm text-gray-600">جميع الشكاوى</p>
      </div>

      {/* Filters Section */}
      <div className="card mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Filtres / المرشحات</h3>
          <button
            onClick={clearFilters}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Effacer les filtres
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {/* Search */}
          <div className="xl:col-span-2">
            <label className="form-label">Recherche</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                className="form-input pl-10"
                placeholder="Numéro, nom, lieu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="form-label">Statut</label>
            <select
              className="form-input"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">Tous les statuts</option>
              <option value="pending">En Attente</option>
              <option value="investigating">En Investigation</option>
              <option value="resolved">Résolue</option>
              <option value="closed">Fermée</option>
            </select>
          </div>

          {/* Priority Filter */}
          <div>
            <label className="form-label">Priorité</label>
            <select
              className="form-input"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
            >
              <option value="">Toutes priorités</option>
              <option value="low">Faible</option>
              <option value="medium">Moyen</option>
              <option value="high">Élevé</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          {/* Type Filter */}
          <div>
            <label className="form-label">Type</label>
            <select
              className="form-input"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="">Tous les types</option>
              {complaintTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name_fr}
                </option>
              ))}
            </select>
          </div>

          {/* Date From */}
          <div>
            <label className="form-label">Date de</label>
            <input
              type="date"
              className="form-input"
              value={dateFromFilter}
              onChange={(e) => setDateFromFilter(e.target.value)}
            />
          </div>

          {/* Date To */}
          <div>
            <label className="form-label">Date à</label>
            <input
              type="date"
              className="form-input"
              value={dateToFilter}
              onChange={(e) => setDateToFilter(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-4 flex items-center justify-between">
        <p className="text-sm text-gray-600">
          {filteredComplaints.length} plainte(s) trouvée(s) sur {complaints.length} au total
        </p>
        <button
          onClick={loadData}
          className="btn-secondary text-sm"
        >
          Actualiser
        </button>
      </div>

      {/* Complaints Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Numéro / التاريخ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plaignant / المشتكي
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type / النوع
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lieu / المكان
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut / الحالة
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priorité / الأولوية
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Officier / الضابط
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredComplaints.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                    <AlertCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium">Aucune plainte trouvée</p>
                    <p className="text-sm">Essayez de modifier vos critères de recherche</p>
                  </td>
                </tr>
              ) : (
                filteredComplaints.map((complaint) => (
                  <tr key={complaint.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {complaint.complaint_number}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(complaint.date_registered)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {complaint.complainant_name}
                          </div>
                          {complaint.complainant_phone && (
                            <div className="text-sm text-gray-500">
                              {complaint.complainant_phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: complaint.color_code }}
                        ></div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {complaint.type_name_fr}
                          </div>
                          <div className="text-sm text-gray-500 text-arabic">
                            {complaint.type_name_ar}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 text-gray-400 mr-2" />
                        <div className="text-sm text-gray-900">
                          {complaint.location_incident}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(complaint.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getPriorityBadge(complaint.priority)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {complaint.officer_in_charge}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => viewComplaintDetails(complaint)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Voir les détails"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          className="text-green-600 hover:text-green-900"
                          title="Modifier"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Complaint Detail Modal */}
      <ComplaintDetailModal
        complaint={selectedComplaint}
        isOpen={showDetailModal}
        onClose={() => {
          setShowDetailModal(false)
          setSelectedComplaint(null)
        }}
      />
    </div>
  )
}

export default AllComplaintsView
