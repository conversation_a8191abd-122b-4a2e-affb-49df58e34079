import Database from 'better-sqlite3';
import { app } from 'electron';
import path from 'path';
import fs from 'fs';

// Database types
export interface Complaint {
  id?: number;
  complaint_number: string;
  date_registered?: string;
  complainant_name: string;
  complainant_address?: string;
  complainant_phone?: string;
  complainant_id_number?: string;
  complaint_type_id: number;
  description_ar?: string;
  description_fr?: string;
  location_incident: string;
  date_incident: string;
  status: 'pending' | 'investigating' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  evidence_notes?: string;
  officer_notes?: string;
  officer_in_charge: string;
  created_at?: string;
  updated_at?: string;
}

export interface ComplaintType {
  id: number;
  name_fr: string;
  name_ar: string;
  color_code: string;
  is_active: boolean;
  created_at?: string;
}

export interface StatusHistory {
  id?: number;
  complaint_id: number;
  old_status?: string;
  new_status: string;
  changed_by?: string;
  notes?: string;
  changed_at?: string;
}

class DatabaseManager {
  private db: Database.Database | null = null;
  private dbPath: string;

  constructor() {
    // Get the user data directory for storing the database
    const userDataPath = app?.getPath('userData') || './data';
    this.dbPath = path.join(userDataPath, 'police_complaints.db');
    
    // Ensure the directory exists
    const dir = path.dirname(this.dbPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  public initialize(): void {
    try {
      this.db = new Database(this.dbPath);
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('foreign_keys = ON');
      
      this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private createTables(): void {
    if (!this.db) throw new Error('Database not initialized');

    // Use fallback schema since we're in ES module context
    const schema = this.getFallbackSchema();

    // Execute schema statements
    const statements = schema.split(';').filter(stmt => stmt.trim());
    for (const statement of statements) {
      if (statement.trim()) {
        this.db.exec(statement);
      }
    }
  }

  private getFallbackSchema(): string {
    return `
      CREATE TABLE IF NOT EXISTS complaints (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        complaint_number TEXT UNIQUE NOT NULL,
        date_registered DATETIME DEFAULT CURRENT_TIMESTAMP,
        complainant_name TEXT NOT NULL,
        complainant_address TEXT,
        complainant_phone TEXT,
        complainant_id_number TEXT,
        complaint_type_id INTEGER NOT NULL,
        description_ar TEXT,
        description_fr TEXT,
        location_incident TEXT NOT NULL,
        date_incident DATE NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'closed')),
        priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        evidence_notes TEXT,
        officer_notes TEXT,
        officer_in_charge TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS complaint_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name_fr TEXT NOT NULL,
        name_ar TEXT NOT NULL,
        color_code TEXT DEFAULT '#3b82f6',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      INSERT OR IGNORE INTO complaint_types (id, name_fr, name_ar, color_code) VALUES
      (1, 'Vol', 'سرقة', '#ef4444'),
      (2, 'Agression', 'اعتداء', '#f97316'),
      (3, 'Fraude', 'احتيال', '#eab308'),
      (4, 'Harcèlement', 'مضايقة', '#8b5cf6'),
      (5, 'Dispute', 'نزاع', '#06b6d4'),
      (6, 'Vandalisme', 'تخريب', '#84cc16'),
      (7, 'Autre', 'أخرى', '#6b7280');
    `;
  }

  public generateComplaintNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    // Get the count of complaints today
    const today = `${year}-${month}-${day}`;
    const count = this.db?.prepare(`
      SELECT COUNT(*) as count 
      FROM complaints 
      WHERE DATE(date_registered) = DATE(?)
    `).get(today) as { count: number };
    
    const sequence = String((count?.count || 0) + 1).padStart(3, '0');
    return `PL${year}${month}${day}${sequence}`;
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  public getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db;
  }
}

// Export singleton instance
export const dbManager = new DatabaseManager();
