"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("ipcRenderer", {
  on(...args) {
    const [channel, listener] = args;
    return electron.ipcRenderer.on(channel, (event, ...args2) => listener(event, ...args2));
  },
  off(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.off(channel, ...omit);
  },
  send(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.send(channel, ...omit);
  },
  invoke(...args) {
    const [channel, ...omit] = args;
    return electron.ipcRenderer.invoke(channel, ...omit);
  }
});
electron.contextBridge.exposeInMainWorld("electronAPI", {
  // Complaint operations
  getComplaintTypes: () => electron.ipcRenderer.invoke("db:getComplaintTypes"),
  createComplaint: (complaint) => electron.ipcRenderer.invoke("db:createComplaint", complaint),
  getComplaints: () => electron.ipcRenderer.invoke("db:getComplaints"),
  getComplaintById: (id) => electron.ipcRenderer.invoke("db:getComplaintById", id),
  updateComplaint: (id, updates) => electron.ipcRenderer.invoke("db:updateComplaint", id, updates),
  getComplaintStats: () => electron.ipcRenderer.invoke("db:getComplaintStats")
});
