# https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#supported-runners-and-hardware-resources

name: visual-studio
on:
  push:
    branches:
      - main
      - release/v*
  pull_request:
    branches: 
      - main
      - release/v*
jobs:
  visual-studio:
    strategy:
      fail-fast: false
      max-parallel: 8
      matrix:
        os: [windows-latest]
        msvs-version: [2016, 2019, 2022]  # https://github.com/actions/virtual-environments/tree/main/images/win
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
      - name: Install Dependencies
        run: |
          npm install --no-progress
          # npm audit fix --force
      - name: Set Windows environment
        if: startsWith(matrix.os, 'windows')
        run: |
          echo 'GYP_MSVS_VERSION=${{ matrix.msvs-version }}' >> $Env:GITHUB_ENV
          echo 'GYP_MSVS_OVERRIDE_PATH=C:\\Dummy' >> $Env:GITHUB_ENV
      - name: Environment Information
        run: npx envinfo
      - name: Run Node tests
        run: npm test
