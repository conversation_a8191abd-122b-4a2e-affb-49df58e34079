import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

export class PDFGenerator {
  private static instance: PDFGenerator
  
  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  /**
   * Generate a detailed complaint report in PDF format
   */
  public async generateComplaintReport(complaint: Complaint): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 20
    let yPosition = margin

    // Set up fonts and colors
    pdf.setFont('helvetica', 'normal')
    
    // Header - Chadian Police Logo and Title
    this.addHeader(pdf, pageWidth, margin)
    yPosition += 40

    // Complaint Title
    pdf.setFontSize(18)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RAPPORT DE PLAINTE / تقرير الشكوى', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Complaint Number and Date
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Numéro: ${complaint.complaint_number}`, margin, yPosition)
    pdf.text(`Date: ${this.formatDate(complaint.date_registered)}`, pageWidth - margin - 60, yPosition)
    yPosition += 15

    // Separator line
    pdf.setLineWidth(0.5)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 10

    // Section 1: General Information
    yPosition = this.addSection(pdf, 'INFORMATIONS GÉNÉRALES / معلومات عامة', yPosition, margin, pageWidth)
    
    const generalInfo = [
      { label: 'Type de Plainte / نوع الشكوى:', value: `${complaint.type_name_fr} / ${complaint.type_name_ar}` },
      { label: 'Statut / الحالة:', value: this.getStatusText(complaint.status) },
      { label: 'Priorité / الأولوية:', value: this.getPriorityText(complaint.priority) },
      { label: 'Lieu de l\'Incident / مكان الحادث:', value: complaint.location_incident },
      { label: 'Date de l\'Incident / تاريخ الحادث:', value: this.formatDate(complaint.date_incident) }
    ]

    yPosition = this.addInfoList(pdf, generalInfo, yPosition, margin, pageWidth)
    yPosition += 10

    // Section 2: Complainant Information
    yPosition = this.addSection(pdf, 'INFORMATIONS DU PLAIGNANT / معلومات المشتكي', yPosition, margin, pageWidth)
    
    const complainantInfo = [
      { label: 'Nom Complet / الاسم الكامل:', value: complaint.complainant_name },
      { label: 'Numéro d\'Identité / رقم الهوية:', value: complaint.complainant_id_number || 'Non spécifié' },
      { label: 'Téléphone / الهاتف:', value: complaint.complainant_phone || 'Non spécifié' },
      { label: 'Adresse / العنوان:', value: complaint.complainant_address || 'Non spécifiée' }
    ]

    yPosition = this.addInfoList(pdf, complainantInfo, yPosition, margin, pageWidth)
    yPosition += 10

    // Section 3: Description
    if (complaint.description_fr || complaint.description_ar) {
      yPosition = this.addSection(pdf, 'DESCRIPTION DE L\'INCIDENT / وصف الحادث', yPosition, margin, pageWidth)
      
      if (complaint.description_fr) {
        pdf.setFont('helvetica', 'bold')
        pdf.text('Description en Français:', margin, yPosition)
        yPosition += 8
        pdf.setFont('helvetica', 'normal')
        yPosition = this.addWrappedText(pdf, complaint.description_fr, yPosition, margin, pageWidth - 2 * margin)
        yPosition += 5
      }

      if (complaint.description_ar) {
        pdf.setFont('helvetica', 'bold')
        pdf.text('الوصف بالعربية:', margin, yPosition)
        yPosition += 8
        pdf.setFont('helvetica', 'normal')
        yPosition = this.addWrappedText(pdf, complaint.description_ar, yPosition, margin, pageWidth - 2 * margin, 'right')
        yPosition += 5
      }
      yPosition += 5
    }

    // Check if we need a new page
    if (yPosition > pageHeight - 60) {
      pdf.addPage()
      yPosition = margin
    }

    // Section 4: Additional Notes
    if (complaint.evidence_notes || complaint.officer_notes) {
      yPosition = this.addSection(pdf, 'NOTES SUPPLÉMENTAIRES / ملاحظات إضافية', yPosition, margin, pageWidth)
      
      if (complaint.evidence_notes) {
        pdf.setFont('helvetica', 'bold')
        pdf.text('Notes sur les Preuves / ملاحظات الأدلة:', margin, yPosition)
        yPosition += 8
        pdf.setFont('helvetica', 'normal')
        yPosition = this.addWrappedText(pdf, complaint.evidence_notes, yPosition, margin, pageWidth - 2 * margin)
        yPosition += 8
      }

      if (complaint.officer_notes) {
        pdf.setFont('helvetica', 'bold')
        pdf.text('Notes de l\'Officier / ملاحظات الضابط:', margin, yPosition)
        yPosition += 8
        pdf.setFont('helvetica', 'normal')
        yPosition = this.addWrappedText(pdf, complaint.officer_notes, yPosition, margin, pageWidth - 2 * margin)
        yPosition += 8
      }
    }

    // Section 5: Official Information
    yPosition = this.addSection(pdf, 'INFORMATIONS OFFICIELLES / معلومات رسمية', yPosition, margin, pageWidth)
    
    const officialInfo = [
      { label: 'Officier Responsable / الضابط المسؤول:', value: complaint.officer_in_charge },
      { label: 'Date de Création / تاريخ الإنشاء:', value: this.formatDate(complaint.created_at || complaint.date_registered) },
      { label: 'Dernière Modification / آخر تعديل:', value: this.formatDate(complaint.updated_at || complaint.date_registered) }
    ]

    yPosition = this.addInfoList(pdf, officialInfo, yPosition, margin, pageWidth)

    // Footer
    this.addFooter(pdf, pageWidth, pageHeight, margin)

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Generate a summary report of multiple complaints
   */
  public async generateSummaryReport(complaints: Complaint[], title: string = 'RAPPORT RÉCAPITULATIF'): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 20
    let yPosition = margin

    // Header
    this.addHeader(pdf, pageWidth, margin)
    yPosition += 40

    // Title
    pdf.setFontSize(18)
    pdf.setFont('helvetica', 'bold')
    pdf.text(`${title} / تقرير ملخص`, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Date and count
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Date du Rapport: ${this.formatDate(new Date().toISOString())}`, margin, yPosition)
    pdf.text(`Nombre de Plaintes: ${complaints.length}`, pageWidth - margin - 60, yPosition)
    yPosition += 15

    // Separator
    pdf.setLineWidth(0.5)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 15

    // Statistics
    const stats = this.calculateStatistics(complaints)
    yPosition = this.addSection(pdf, 'STATISTIQUES / إحصائيات', yPosition, margin, pageWidth)
    
    const statsInfo = [
      { label: 'Total des Plaintes:', value: stats.total.toString() },
      { label: 'En Attente:', value: stats.pending.toString() },
      { label: 'En Investigation:', value: stats.investigating.toString() },
      { label: 'Résolues:', value: stats.resolved.toString() },
      { label: 'Fermées:', value: stats.closed.toString() }
    ]

    yPosition = this.addInfoList(pdf, statsInfo, yPosition, margin, pageWidth)
    yPosition += 15

    // Complaints table
    yPosition = this.addSection(pdf, 'LISTE DES PLAINTES / قائمة الشكاوى', yPosition, margin, pageWidth)
    yPosition = this.addComplaintsTable(pdf, complaints, yPosition, margin, pageWidth, pageHeight)

    // Footer
    this.addFooter(pdf, pageWidth, pageHeight, margin)

    // Save the PDF
    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  private addHeader(pdf: jsPDF, pageWidth: number, margin: number): void {
    // Chadian Police Header
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RÉPUBLIQUE DU TCHAD', pageWidth / 2, margin, { align: 'center' })
    pdf.text('جمهورية تشاد', pageWidth / 2, margin + 8, { align: 'center' })
    
    pdf.setFontSize(14)
    pdf.text('POLICE NATIONALE', pageWidth / 2, margin + 20, { align: 'center' })
    pdf.text('الشرطة الوطنية', pageWidth / 2, margin + 28, { align: 'center' })
    
    // Line under header
    pdf.setLineWidth(1)
    pdf.line(margin, margin + 35, pageWidth - margin, margin + 35)
  }

  private addFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, margin: number): void {
    const footerY = pageHeight - margin
    
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'normal')
    pdf.text('Document généré automatiquement par le Système de Gestion des Plaintes', pageWidth / 2, footerY - 10, { align: 'center' })
    pdf.text('وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى', pageWidth / 2, footerY - 5, { align: 'center' })
    
    // Page number
    const pageNum = pdf.internal.getCurrentPageInfo().pageNumber
    pdf.text(`Page ${pageNum}`, pageWidth - margin, footerY, { align: 'right' })
  }

  private addSection(pdf: jsPDF, title: string, yPosition: number, margin: number, pageWidth: number): number {
    pdf.setFontSize(14)
    pdf.setFont('helvetica', 'bold')
    pdf.text(title, margin, yPosition)
    yPosition += 10
    
    // Underline
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 8
    
    return yPosition
  }

  private addInfoList(pdf: jsPDF, items: Array<{label: string, value: string}>, yPosition: number, margin: number, pageWidth: number): number {
    pdf.setFontSize(10)
    
    items.forEach(item => {
      pdf.setFont('helvetica', 'bold')
      pdf.text(item.label, margin, yPosition)
      pdf.setFont('helvetica', 'normal')
      pdf.text(item.value, margin + 60, yPosition)
      yPosition += 6
    })
    
    return yPosition
  }

  private addWrappedText(pdf: jsPDF, text: string, yPosition: number, x: number, maxWidth: number, align: 'left' | 'right' = 'left'): number {
    const lines = pdf.splitTextToSize(text, maxWidth)
    
    lines.forEach((line: string) => {
      const xPos = align === 'right' ? x + maxWidth : x
      pdf.text(line, xPos, yPosition, { align })
      yPosition += 5
    })
    
    return yPosition
  }

  private addComplaintsTable(pdf: jsPDF, complaints: Complaint[], yPosition: number, margin: number, pageWidth: number, pageHeight: number): number {
    const tableWidth = pageWidth - 2 * margin
    const colWidths = [30, 50, 40, 30, 30] // Numéro, Plaignant, Type, Statut, Date
    
    // Table header
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'bold')
    
    let xPos = margin
    const headers = ['Numéro', 'Plaignant', 'Type', 'Statut', 'Date']
    headers.forEach((header, index) => {
      pdf.text(header, xPos, yPosition)
      xPos += colWidths[index]
    })
    yPosition += 8
    
    // Header line
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 5
    
    // Table rows
    pdf.setFont('helvetica', 'normal')
    complaints.forEach(complaint => {
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = margin + 20
      }
      
      xPos = margin
      const rowData = [
        complaint.complaint_number,
        complaint.complainant_name.substring(0, 20) + (complaint.complainant_name.length > 20 ? '...' : ''),
        (complaint.type_name_fr || '').substring(0, 15),
        this.getStatusText(complaint.status).split(' / ')[0],
        this.formatDate(complaint.date_registered).split(' ')[0]
      ]
      
      rowData.forEach((data, index) => {
        pdf.text(data, xPos, yPosition)
        xPos += colWidths[index]
      })
      yPosition += 6
    })
    
    return yPosition + 10
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  private getStatusText(status: string): string {
    const statusMap = {
      pending: 'En Attente / في الانتظار',
      investigating: 'En Investigation / قيد التحقيق',
      resolved: 'Résolue / محلولة',
      closed: 'Fermée / مغلقة'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  private getPriorityText(priority: string): string {
    const priorityMap = {
      low: 'Faible / منخفض',
      medium: 'Moyen / متوسط',
      high: 'Élevé / عالي',
      urgent: 'Urgent / عاجل'
    }
    return priorityMap[priority as keyof typeof priorityMap] || priority
  }

  private calculateStatistics(complaints: Complaint[]) {
    return {
      total: complaints.length,
      pending: complaints.filter(c => c.status === 'pending').length,
      investigating: complaints.filter(c => c.status === 'investigating').length,
      resolved: complaints.filter(c => c.status === 'resolved').length,
      closed: complaints.filter(c => c.status === 'closed').length
    }
  }
}

export const pdfGenerator = PDFGenerator.getInstance()
