import jsPD<PERSON> from 'jspdf'
import html2canvas from 'html2canvas'

// Arabic text processing utilities
class ArabicTextProcessor {
  // Process Arabic text for better PDF rendering
  static processArabicText(text: string): string {
    if (!text) return ''

    // Basic Arabic character detection
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/

    if (arabicRegex.test(text)) {
      // Normalize Unicode characters for better rendering
      try {
        // Use Unicode normalization to ensure proper character encoding
        return text.normalize('NFC')
      } catch (error) {
        console.warn('Unicode normalization failed, using original text:', error)
        return text
      }
    }

    return text
  }

  // Check if text contains Arabic characters
  static isArabicText(text: string): boolean {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/
    return arabicRegex.test(text)
  }

  // Ensure text is properly encoded for PDF
  static encodePDFText(text: string): string {
    if (!text) return ''

    // Replace problematic characters that might not render well in PDF
    return text
      .replace(/[\u200E\u200F]/g, '') // Remove LTR/RTL marks
      .replace(/[\u202A-\u202E]/g, '') // Remove directional formatting
      .normalize('NFC') // Normalize to composed form
  }
}

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

export class PDFGenerator {
  private static instance: PDFGenerator
  
  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  /**
   * Generate a detailed complaint report in PDF format with proper bilingual layout
   */
  public async generateComplaintReport(complaint: Complaint): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    const columnWidth = (pageWidth - 3 * margin) / 2 // Two columns with center margin
    let yPosition = margin

    // Set up fonts - use helvetica which has better Unicode support
    pdf.setFont('helvetica', 'normal')

    // Header - Chadian Police Logo and Title
    this.addBilingualHeader(pdf, pageWidth, margin)
    yPosition += 45

    // Complaint Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RAPPORT DE PLAINTE', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 8
    pdf.text('تقرير الشكوى', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Complaint Number and Date
    pdf.setFontSize(11)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Numéro: ${complaint.complaint_number}`, margin, yPosition)
    pdf.text(`رقم: ${complaint.complaint_number}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 6
    pdf.text(`Date: ${this.formatDate(complaint.date_registered)}`, margin, yPosition)
    pdf.text(`التاريخ: ${this.formatDate(complaint.date_registered)}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 15

    // Separator line
    pdf.setLineWidth(0.5)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 12

    // Section 1: General Information
    yPosition = this.addBilingualSection(pdf, 'INFORMATIONS GÉNÉRALES', 'معلومات عامة', yPosition, margin, pageWidth)

    // Type de plainte
    yPosition = this.addBilingualField(pdf, 'Type de Plainte:', 'نوع الشكوى:',
      complaint.type_name_fr || 'Non spécifié', complaint.type_name_ar || 'غير محدد',
      yPosition, margin, columnWidth)

    // Statut
    const statusFr = this.getStatusText(complaint.status).split(' / ')[0]
    const statusAr = this.getStatusText(complaint.status).split(' / ')[1] || statusFr
    yPosition = this.addBilingualField(pdf, 'Statut:', 'الحالة:', statusFr, statusAr, yPosition, margin, columnWidth)

    // Priorité
    const priorityFr = this.getPriorityText(complaint.priority).split(' / ')[0]
    const priorityAr = this.getPriorityText(complaint.priority).split(' / ')[1] || priorityFr
    yPosition = this.addBilingualField(pdf, 'Priorité:', 'الأولوية:', priorityFr, priorityAr, yPosition, margin, columnWidth)

    // Lieu
    yPosition = this.addBilingualField(pdf, 'Lieu de l\'Incident:', 'مكان الحادث:',
      complaint.location_incident, complaint.location_incident, yPosition, margin, columnWidth)

    // Date incident
    const incidentDate = this.formatDate(complaint.date_incident)
    yPosition = this.addBilingualField(pdf, 'Date de l\'Incident:', 'تاريخ الحادث:',
      incidentDate, incidentDate, yPosition, margin, columnWidth)

    yPosition += 10

    // Section 2: Complainant Information
    yPosition = this.addBilingualSection(pdf, 'INFORMATIONS DU PLAIGNANT', 'معلومات المشتكي', yPosition, margin, pageWidth)

    yPosition = this.addBilingualField(pdf, 'Nom Complet:', 'الاسم الكامل:',
      complaint.complainant_name, complaint.complainant_name, yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'Numéro d\'Identité:', 'رقم الهوية:',
      complaint.complainant_id_number || 'Non spécifié', complaint.complainant_id_number || 'غير محدد',
      yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'Téléphone:', 'الهاتف:',
      complaint.complainant_phone || 'Non spécifié', complaint.complainant_phone || 'غير محدد',
      yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'Adresse:', 'العنوان:',
      complaint.complainant_address || 'Non spécifiée', complaint.complainant_address || 'غير محدد',
      yPosition, margin, columnWidth)

    yPosition += 10

    // Check if we need a new page
    if (yPosition > pageHeight - 80) {
      pdf.addPage()
      yPosition = margin + 20
    }

    // Section 3: Description
    yPosition = this.addBilingualSection(pdf, 'DESCRIPTION DE L\'INCIDENT', 'وصف الحادث', yPosition, margin, pageWidth)

    // Always show both descriptions, use placeholder if one is missing
    const descriptionFr = complaint.description_fr || '[Description non fournie en français]'
    const descriptionAr = complaint.description_ar || '[لم يتم تقديم وصف بالعربية]'

    yPosition = this.addBilingualTextBlock(pdf, 'Description en Français:', 'الوصف بالعربية:',
      descriptionFr, descriptionAr, yPosition, margin, columnWidth)

    yPosition += 10

    // Section 4: Additional Notes (if any)
    if (complaint.evidence_notes || complaint.officer_notes) {
      if (yPosition > pageHeight - 60) {
        pdf.addPage()
        yPosition = margin + 20
      }

      yPosition = this.addBilingualSection(pdf, 'NOTES SUPPLÉMENTAIRES', 'ملاحظات إضافية', yPosition, margin, pageWidth)

      if (complaint.evidence_notes) {
        yPosition = this.addBilingualTextBlock(pdf, 'Notes sur les Preuves:', 'ملاحظات الأدلة:',
          complaint.evidence_notes, complaint.evidence_notes, yPosition, margin, columnWidth)
        yPosition += 8
      }

      if (complaint.officer_notes) {
        yPosition = this.addBilingualTextBlock(pdf, 'Notes de l\'Officier:', 'ملاحظات الضابط:',
          complaint.officer_notes, complaint.officer_notes, yPosition, margin, columnWidth)
        yPosition += 8
      }
    }

    // Section 5: Official Information
    if (yPosition > pageHeight - 50) {
      pdf.addPage()
      yPosition = margin + 20
    }

    yPosition = this.addBilingualSection(pdf, 'INFORMATIONS OFFICIELLES', 'معلومات رسمية', yPosition, margin, pageWidth)

    yPosition = this.addBilingualField(pdf, 'Officier Responsable:', 'الضابط المسؤول:',
      complaint.officer_in_charge, complaint.officer_in_charge, yPosition, margin, columnWidth)

    const createdDate = this.formatDate(complaint.created_at || complaint.date_registered)
    yPosition = this.addBilingualField(pdf, 'Date de Création:', 'تاريخ الإنشاء:',
      createdDate, createdDate, yPosition, margin, columnWidth)

    const updatedDate = this.formatDate(complaint.updated_at || complaint.date_registered)
    yPosition = this.addBilingualField(pdf, 'Dernière Modification:', 'آخر تعديل:',
      updatedDate, updatedDate, yPosition, margin, columnWidth)

    // Footer
    this.addBilingualFooter(pdf, pageWidth, pageHeight, margin)

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Generate a summary report of multiple complaints with bilingual layout
   */
  public async generateSummaryReport(complaints: Complaint[], title: string = 'RAPPORT RÉCAPITULATIF'): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    let yPosition = margin

    // Header
    this.addBilingualHeader(pdf, pageWidth, margin)
    yPosition += 45

    // Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text(title, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 8
    pdf.text('تقرير ملخص', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Date and count
    pdf.setFontSize(10)
    pdf.setFont('helvetica', 'normal')
    const reportDate = this.formatDate(new Date().toISOString())
    pdf.text(`Date du Rapport: ${reportDate}`, margin, yPosition)
    pdf.text(`تاريخ التقرير: ${reportDate}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 6
    pdf.text(`Nombre de Plaintes: ${complaints.length}`, margin, yPosition)
    pdf.text(`عدد الشكاوى: ${complaints.length}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 15

    // Separator
    pdf.setLineWidth(0.5)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 15

    // Statistics
    const stats = this.calculateStatistics(complaints)
    yPosition = this.addBilingualSection(pdf, 'STATISTIQUES', 'إحصائيات', yPosition, margin, pageWidth)

    const columnWidth = (pageWidth - 3 * margin) / 2

    yPosition = this.addBilingualField(pdf, 'Total des Plaintes:', 'إجمالي الشكاوى:',
      stats.total.toString(), stats.total.toString(), yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'En Attente:', 'في الانتظار:',
      stats.pending.toString(), stats.pending.toString(), yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'En Investigation:', 'قيد التحقيق:',
      stats.investigating.toString(), stats.investigating.toString(), yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'Résolues:', 'محلولة:',
      stats.resolved.toString(), stats.resolved.toString(), yPosition, margin, columnWidth)

    yPosition = this.addBilingualField(pdf, 'Fermées:', 'مغلقة:',
      stats.closed.toString(), stats.closed.toString(), yPosition, margin, columnWidth)

    yPosition += 15

    // Complaints table
    yPosition = this.addBilingualSection(pdf, 'LISTE DES PLAINTES', 'قائمة الشكاوى', yPosition, margin, pageWidth)
    yPosition = this.addBilingualComplaintsTable(pdf, complaints, yPosition, margin, pageWidth, pageHeight)

    // Footer
    this.addBilingualFooter(pdf, pageWidth, pageHeight, margin)

    // Save the PDF
    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  private addBilingualHeader(pdf: jsPDF, pageWidth: number, margin: number): void {
    // Chadian Police Header with better spacing and proper Arabic encoding
    pdf.setFontSize(14)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RÉPUBLIQUE DU TCHAD', pageWidth / 2, margin + 5, { align: 'center' })
    pdf.text(ArabicTextProcessor.encodePDFText('جمهورية تشاد'), pageWidth / 2, margin + 15, { align: 'center' })

    pdf.setFontSize(12)
    pdf.text('POLICE NATIONALE', pageWidth / 2, margin + 25, { align: 'center' })
    pdf.text(ArabicTextProcessor.encodePDFText('الشرطة الوطنية'), pageWidth / 2, margin + 35, { align: 'center' })

    // Line under header
    pdf.setLineWidth(1)
    pdf.line(margin, margin + 42, pageWidth - margin, margin + 42)
  }

  private addBilingualSection(pdf: jsPDF, titleFr: string, titleAr: string, yPosition: number, margin: number, pageWidth: number): number {
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'bold')

    // French title on left
    pdf.text(titleFr, margin, yPosition)

    // Arabic title on right with proper encoding
    pdf.text(ArabicTextProcessor.encodePDFText(titleAr), pageWidth - margin, yPosition, { align: 'right' })

    yPosition += 8

    // Underline
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 10

    return yPosition
  }

  private addBilingualField(pdf: jsPDF, labelFr: string, labelAr: string, valueFr: string, valueAr: string,
                           yPosition: number, margin: number, columnWidth: number): number {
    pdf.setFontSize(9)

    // Process text for better rendering
    const processedLabelAr = ArabicTextProcessor.encodePDFText(labelAr)
    const processedValueAr = ArabicTextProcessor.encodePDFText(valueAr)

    // Left column (French)
    pdf.setFont('helvetica', 'bold')
    pdf.text(labelFr, margin, yPosition)
    pdf.setFont('helvetica', 'normal')

    // Wrap French text if too long
    const frenchLines = pdf.splitTextToSize(valueFr, columnWidth - 5)
    let currentY = yPosition + 5
    frenchLines.forEach((line: string) => {
      pdf.text(line, margin, currentY)
      currentY += 4
    })

    // Right column (Arabic)
    const rightColumnX = margin + columnWidth + 10
    pdf.setFont('helvetica', 'bold')
    pdf.text(processedLabelAr, margin + 2 * columnWidth + 10, yPosition, { align: 'right' })
    pdf.setFont('helvetica', 'normal')

    // Wrap Arabic text if too long
    const arabicLines = pdf.splitTextToSize(processedValueAr, columnWidth - 5)
    currentY = yPosition + 5
    arabicLines.forEach((line: string) => {
      pdf.text(line, margin + 2 * columnWidth + 10, currentY, { align: 'right' })
      currentY += 4
    })

    return Math.max(yPosition + 5 + (frenchLines.length * 4), yPosition + 5 + (arabicLines.length * 4)) + 3
  }

  private addBilingualTextBlock(pdf: jsPDF, labelFr: string, labelAr: string, textFr: string, textAr: string,
                               yPosition: number, margin: number, columnWidth: number): number {
    pdf.setFontSize(9)

    // Process Arabic text for better rendering
    const processedLabelAr = ArabicTextProcessor.encodePDFText(labelAr)
    const processedTextAr = ArabicTextProcessor.encodePDFText(textAr)

    // French section (left column)
    pdf.setFont('helvetica', 'bold')
    pdf.text(labelFr, margin, yPosition)
    yPosition += 6

    pdf.setFont('helvetica', 'normal')
    const frenchLines = pdf.splitTextToSize(textFr, columnWidth - 5)
    let frenchEndY = yPosition
    frenchLines.forEach((line: string) => {
      pdf.text(line, margin, frenchEndY)
      frenchEndY += 4
    })

    // Arabic section (right column)
    const rightColumnX = margin + columnWidth + 10
    let arabicStartY = yPosition - 6

    pdf.setFont('helvetica', 'bold')
    pdf.text(processedLabelAr, margin + 2 * columnWidth + 10, arabicStartY, { align: 'right' })
    arabicStartY += 6

    pdf.setFont('helvetica', 'normal')
    const arabicLines = pdf.splitTextToSize(processedTextAr, columnWidth - 5)
    let arabicEndY = arabicStartY
    arabicLines.forEach((line: string) => {
      pdf.text(line, margin + 2 * columnWidth + 10, arabicEndY, { align: 'right' })
      arabicEndY += 4
    })

    return Math.max(frenchEndY, arabicEndY) + 5
  }

  private addBilingualFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, margin: number): void {
    const footerY = pageHeight - margin

    pdf.setFontSize(7)
    pdf.setFont('helvetica', 'normal')
    pdf.text('Document généré automatiquement par le Système de Gestion des Plaintes', pageWidth / 2, footerY - 12, { align: 'center' })
    pdf.text(ArabicTextProcessor.encodePDFText('وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى'), pageWidth / 2, footerY - 7, { align: 'center' })

    // Page number
    const pageNum = pdf.internal.getCurrentPageInfo().pageNumber
    pdf.text(`Page ${pageNum}`, pageWidth - margin, footerY - 2, { align: 'right' })
    pdf.text(ArabicTextProcessor.encodePDFText(`صفحة ${pageNum}`), margin, footerY - 2)
  }

  // Legacy methods removed - using new bilingual methods instead

  private addBilingualComplaintsTable(pdf: jsPDF, complaints: Complaint[], yPosition: number, margin: number, pageWidth: number, pageHeight: number): number {
    const tableWidth = pageWidth - 2 * margin
    const colWidths = [25, 35, 35, 25, 25, 25] // Numéro, Plaignant, Type, Statut, Date, Officier

    // Table header - bilingual
    pdf.setFontSize(7)
    pdf.setFont('helvetica', 'bold')

    let xPos = margin
    const headersFr = ['Numéro', 'Plaignant', 'Type', 'Statut', 'Date', 'Officier']
    const headersAr = ['رقم', 'المشتكي', 'النوع', 'الحالة', 'التاريخ', 'الضابط']

    // French headers
    headersFr.forEach((header, index) => {
      pdf.text(header, xPos, yPosition)
      xPos += colWidths[index]
    })
    yPosition += 4

    // Arabic headers
    xPos = margin
    headersAr.forEach((header, index) => {
      pdf.text(header, xPos + colWidths[index], yPosition, { align: 'right' })
      xPos += colWidths[index]
    })
    yPosition += 8

    // Header line
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 5

    // Table rows
    pdf.setFont('helvetica', 'normal')
    complaints.forEach(complaint => {
      if (yPosition > pageHeight - 40) {
        pdf.addPage()
        yPosition = margin + 20

        // Repeat headers on new page
        pdf.setFont('helvetica', 'bold')
        xPos = margin
        headersFr.forEach((header, index) => {
          pdf.text(header, xPos, yPosition)
          xPos += colWidths[index]
        })
        yPosition += 4

        xPos = margin
        headersAr.forEach((header, index) => {
          pdf.text(header, xPos + colWidths[index], yPosition, { align: 'right' })
          xPos += colWidths[index]
        })
        yPosition += 8

        pdf.setLineWidth(0.3)
        pdf.line(margin, yPosition, pageWidth - margin, yPosition)
        yPosition += 5
        pdf.setFont('helvetica', 'normal')
      }

      xPos = margin
      const rowData = [
        complaint.complaint_number,
        complaint.complainant_name.substring(0, 15) + (complaint.complainant_name.length > 15 ? '...' : ''),
        (complaint.type_name_fr || '').substring(0, 12),
        this.getStatusText(complaint.status).split(' / ')[0].substring(0, 10),
        this.formatDate(complaint.date_registered).split(' ')[0],
        complaint.officer_in_charge.substring(0, 12) + (complaint.officer_in_charge.length > 12 ? '...' : '')
      ]

      rowData.forEach((data, index) => {
        pdf.text(data, xPos, yPosition)
        xPos += colWidths[index]
      })
      yPosition += 5
    })

    return yPosition + 10
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  private getStatusText(status: string): string {
    const statusMap = {
      pending: 'En Attente / في الانتظار',
      investigating: 'En Investigation / قيد التحقيق',
      resolved: 'Résolue / محلولة',
      closed: 'Fermée / مغلقة'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  private getPriorityText(priority: string): string {
    const priorityMap = {
      low: 'Faible / منخفض',
      medium: 'Moyen / متوسط',
      high: 'Élevé / عالي',
      urgent: 'Urgent / عاجل'
    }
    return priorityMap[priority as keyof typeof priorityMap] || priority
  }

  private calculateStatistics(complaints: Complaint[]) {
    return {
      total: complaints.length,
      pending: complaints.filter(c => c.status === 'pending').length,
      investigating: complaints.filter(c => c.status === 'investigating').length,
      resolved: complaints.filter(c => c.status === 'resolved').length,
      closed: complaints.filter(c => c.status === 'closed').length
    }
  }
}

export const pdfGenerator = PDFGenerator.getInstance()
