import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// Arabic text processing utilities with proper font handling
class ArabicTextProcessor {
  // Create a canvas element to render Arabic text properly
  static async renderArabicTextToCanvas(text: string, fontSize: number = 12, fontFamily: string = 'Arial'): Promise<string> {
    if (!text) return ''

    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        resolve(text) // Fallback to original text
        return
      }

      // Set canvas size based on text
      canvas.width = 800
      canvas.height = 100

      // Configure context for Arabic text
      ctx.font = `${fontSize}px ${fontFamily}, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif`
      ctx.textAlign = 'right'
      ctx.direction = 'rtl'
      ctx.fillStyle = 'black'

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw text
      ctx.fillText(text, canvas.width - 10, fontSize + 10)

      // Convert to data URL
      const dataURL = canvas.toDataURL('image/png')
      resolve(dataURL)
    })
  }

  // Check if text contains Arabic characters
  static isArabicText(text: string): boolean {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/
    return arabicRegex.test(text)
  }

  // Process text for better rendering
  static processText(text: string): string {
    if (!text) return ''

    // Normalize Unicode and clean up
    return text
      .replace(/[\u200E\u200F]/g, '') // Remove LTR/RTL marks
      .replace(/[\u202A-\u202E]/g, '') // Remove directional formatting
      .normalize('NFC') // Normalize to composed form
      .trim()
  }
}

interface Complaint {
  id: number
  complaint_number: string
  date_registered: string
  complainant_name: string
  complainant_address?: string
  complainant_phone?: string
  complainant_id_number?: string
  complaint_type_id: number
  description_ar?: string
  description_fr?: string
  location_incident: string
  date_incident: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  evidence_notes?: string
  officer_notes?: string
  officer_in_charge: string
  created_at?: string
  updated_at?: string
  type_name_fr?: string
  type_name_ar?: string
  color_code?: string
}

export class PDFGenerator {
  private static instance: PDFGenerator
  
  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  /**
   * Generate a detailed complaint report in PDF format with proper pagination
   */
  public async generateComplaintReport(complaint: Complaint): Promise<void> {
    try {
      // Try HTML-to-canvas approach with pagination
      await this.generatePaginatedPDF(complaint)
    } catch (error) {
      console.error('Error generating paginated PDF:', error)
      // Fallback to text-based PDF with pagination
      await this.generateTextBasedPDF(complaint)
    }
  }

  /**
   * Generate PDF using HTML-to-canvas with proper pagination
   */
  private async generatePaginatedPDF(complaint: Complaint): Promise<void> {
    // Create HTML content sections
    const sections = this.createPaginatedHTML(complaint)
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()

    for (let i = 0; i < sections.length; i++) {
      if (i > 0) {
        pdf.addPage()
      }

      // Create temporary div for this section
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = sections[i]
      tempDiv.style.position = 'absolute'
      tempDiv.style.left = '-9999px'
      tempDiv.style.width = '210mm'
      tempDiv.style.height = '297mm'
      tempDiv.style.fontFamily = 'Arial, "Noto Naskh Arabic", "Amiri", "Arabic Typesetting", sans-serif'
      tempDiv.style.padding = '20px'
      tempDiv.style.boxSizing = 'border-box'
      tempDiv.style.backgroundColor = '#ffffff'
      document.body.appendChild(tempDiv)

      try {
        // Convert section to canvas
        const canvas = await html2canvas(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: 794,
          height: 1123,
          scrollX: 0,
          scrollY: 0
        })

        // Add to PDF
        const imgData = canvas.toDataURL('image/png')
        pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight)
      } finally {
        document.body.removeChild(tempDiv)
      }
    }

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Create paginated HTML sections
   */
  private createPaginatedHTML(complaint: Complaint): string[] {
    const sections: string[] = []

    // Page 1: Header + Basic Info + Complainant Info
    sections.push(this.createPage1HTML(complaint))

    // Page 2: Description + Additional Info (if needed)
    const page2Content = this.createPage2HTML(complaint)
    if (page2Content.trim()) {
      sections.push(page2Content)
    }

    return sections
  }

  /**
   * Create first page HTML
   */
  private createPage1HTML(complaint: Complaint): string {
    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      ">
        ${this.getOfficialHeader()}

        <!-- Title -->
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0;">RAPPORT DE PLAINTE</h2>
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0; direction: rtl;">تقرير الشكوى</h2>
        </div>

        <!-- Complaint Info -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <div>
            <strong>Numéro:</strong> ${complaint.complaint_number}<br>
            <strong>Date:</strong> ${this.formatDate(complaint.date_registered)}
          </div>
          <div style="text-align: right; direction: rtl;">
            <strong>رقم:</strong> ${complaint.complaint_number}<br>
            <strong>التاريخ:</strong> ${this.formatDate(complaint.date_registered)}
          </div>
        </div>

        <!-- General Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS GÉNÉRALES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات عامة</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Type de Plainte:</strong><br>
                ${complaint.type_name_fr || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Lieu de l'Incident:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de l'Incident:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>نوع الشكوى:</strong><br>
                ${complaint.type_name_ar || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>مكان الحادث:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الحادث:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>
          </div>
        </div>

        <!-- Complainant Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS DU PLAIGNANT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكي</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom Complet:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Numéro d'Identité:</strong><br>
                ${complaint.complainant_id_number || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Téléphone:</strong><br>
                ${complaint.complainant_phone || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Adresse:</strong><br>
                ${complaint.complainant_address || 'Non spécifiée'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الاسم الكامل:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>رقم الهوية:</strong><br>
                ${complaint.complainant_id_number || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>الهاتف:</strong><br>
                ${complaint.complainant_phone || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>العنوان:</strong><br>
                ${complaint.complainant_address || 'غير محدد'}
              </div>
            </div>
          </div>
        </div>

        <!-- Accused Information (Always show section, even if empty) -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS SUR LE MIS EN CAUSE</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكى عليه</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom du Mis en Cause:</strong><br>
                ${(complaint as any).accused_name || '[Non spécifié / À déterminer]'}
              </div>
              ${(complaint as any).accused_name ? `
              <div style="margin-bottom: 8px;">
                <strong>Informations Supplémentaires:</strong><br>
                [À compléter lors de l'enquête]
              </div>
              ` : `
              <div style="margin-bottom: 8px; font-style: italic; color: #666;">
                Les informations sur le mis en cause seront ajoutées<br>
                lors de l'avancement de l'enquête.
              </div>
              `}
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>اسم المشتكى عليه:</strong><br>
                ${(complaint as any).accused_name || '[غير محدد / سيتم تحديده]'}
              </div>
              ${(complaint as any).accused_name ? `
              <div style="margin-bottom: 8px;">
                <strong>معلومات إضافية:</strong><br>
                [سيتم استكمالها أثناء التحقيق]
              </div>
              ` : `
              <div style="margin-bottom: 8px; font-style: italic; color: #666; direction: rtl;">
                سيتم إضافة معلومات المشتكى عليه<br>
                عند تقدم التحقيق.
              </div>
              `}
            </div>
          </div>
        </div>

        <!-- Page Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Page 1 - Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">صفحة 1 - وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Create second page HTML for description and additional info
   */
  private createPage2HTML(complaint: Complaint): string {
    const hasDescription = complaint.description_fr || complaint.description_ar
    const hasNotes = complaint.evidence_notes || complaint.officer_notes

    if (!hasDescription && !hasNotes) {
      return '' // No second page needed
    }

    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      ">
        ${this.getOfficialHeaderSmall()}

        <!-- Description -->
        ${hasDescription ? `
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">DESCRIPTION DE L'INCIDENT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">وصف الحادث</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <strong>Description en Français:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd; min-height: 100px;">
                ${complaint.description_fr || '[Description non fournie en français]'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <strong>الوصف بالعربية:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd; text-align: right; direction: rtl; min-height: 100px;">
                ${complaint.description_ar || '[لم يتم تقديم وصف بالعربية]'}
              </div>
            </div>
          </div>
        </div>
        ` : ''}

        ${hasNotes ? `
        <!-- Additional Notes -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">NOTES SUPPLÉMENTAIRES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">ملاحظات إضافية</h3>
          </div>

          ${complaint.evidence_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes sur les Preuves:</strong>
              <strong style="direction: rtl;">ملاحظات الأدلة:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.evidence_notes}
            </div>
          </div>
          ` : ''}

          ${complaint.officer_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes de l'Officier:</strong>
              <strong style="direction: rtl;">ملاحظات الضابط:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.officer_notes}
            </div>
          </div>
          ` : ''}
        </div>
        ` : ''}

        <!-- Official Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS OFFICIELLES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات رسمية</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Officier Responsable:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de Création:</strong><br>
                ${this.formatDate(complaint.created_at || complaint.date_registered)}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Dernière Modification:</strong><br>
                ${this.formatDate(complaint.updated_at || complaint.date_registered)}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الضابط المسؤول:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الإنشاء:</strong><br>
                ${this.formatDate(complaint.created_at || complaint.date_registered)}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>آخر تعديل:</strong><br>
                ${this.formatDate(complaint.updated_at || complaint.date_registered)}
              </div>
            </div>
          </div>
        </div>

        <!-- Signature and Stamp Section -->
        <div style="margin-bottom: 40px; margin-top: 40px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">VALIDATION OFFICIELLE</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">التصديق الرسمي</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
            <!-- Signature Section -->
            <div style="text-align: center;">
              <div style="margin-bottom: 15px;">
                <strong style="font-size: 12px;">Le Commissaire</strong><br>
                <span style="font-size: 10px; font-style: italic;">Signature</span>
              </div>
              <div style="
                border: 1px solid #000;
                height: 80px;
                margin: 10px 0;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #666;
              ">
                [Signature du Commissaire]
              </div>
            </div>

            <!-- Stamp Section -->
            <div style="text-align: center; direction: rtl;">
              <div style="margin-bottom: 15px;">
                <strong style="font-size: 12px;">المفوض</strong><br>
                <span style="font-size: 10px; font-style: italic;">التوقيع</span>
              </div>
              <div style="
                border: 1px solid #000;
                height: 80px;
                margin: 10px 0;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #666;
                direction: rtl;
              ">
                [الختم الرسمي]
              </div>
            </div>
          </div>

          <!-- Official Stamp Area -->
          <div style="text-align: center; margin-top: 30px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <strong style="font-size: 12px;">CACHET OFFICIEL</strong>
              <strong style="font-size: 12px; direction: rtl;">الختم الرسمي</strong>
            </div>
            <div style="
              border: 2px solid #000;
              height: 100px;
              width: 200px;
              margin: 0 auto;
              background: #f9f9f9;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              color: #666;
              border-radius: 50%;
            ">
              [CACHET OFFICIEL<br>DE LA POLICE NATIONALE<br>DU TCHAD]
            </div>
          </div>
        </div>

        <!-- Page Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Page 2 - Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">صفحة 2 - وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Get official header for subsequent pages (smaller)
   */
  private getOfficialHeaderSmall(): string {
    return `
      <div style="margin-bottom: 20px; border-bottom: 1px solid #000; padding-bottom: 10px;">
        <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
          <!-- Left Side (French) -->
          <div style="text-align: left; font-size: 9px; line-height: 1.2;">
            <div style="font-weight: bold; font-size: 12px; margin-bottom: 5px;">RÉPUBLIQUE DU TCHAD</div>
            <div style="margin-bottom: 2px;">Direction Générale de la Police Nationale</div>
          </div>

          <!-- Center (Official Logo - Smaller) -->
          <div style="text-align: center;">
            <img src="/police-logo.svg" alt="Police Nationale du Tchad" style="
              width: 50px;
              height: 50px;
              margin: 0 auto;
              display: block;
            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
            <div style="
              width: 50px;
              height: 50px;
              border: 1px solid #000;
              border-radius: 50%;
              display: none;
              align-items: center;
              justify-content: center;
              font-size: 8px;
              font-weight: bold;
              background: #f9f9f9;
              margin: 0 auto;
            ">
              POLICE
            </div>
          </div>

          <!-- Right Side (Arabic) -->
          <div style="text-align: right; font-size: 9px; line-height: 1.2; direction: rtl;">
            <div style="font-weight: bold; font-size: 12px; margin-bottom: 5px;">جمهورية تشاد</div>
            <div style="margin-bottom: 2px;">الإدارة العامة للشرطة الوطنية</div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Get full official header with real logo
   */
  private getOfficialHeader(): string {
    return `
      <div style="margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 15px;">
        <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: center;">
          <!-- Left Side (French) -->
          <div style="text-align: left; font-size: 11px; line-height: 1.3;">
            <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">RÉPUBLIQUE DU TCHAD</div>
            <div style="margin-bottom: 3px;">Présidence de la République</div>
            <div style="margin-bottom: 3px;">Ministère de la Sécurité Publique et de l'Immigration</div>
            <div style="margin-bottom: 3px;">Direction Générale de la Police Nationale</div>
            <div style="margin-bottom: 3px;">Direction de la Sécurité Publique</div>
            <div style="margin-bottom: 3px;">Commissariat Central de Sécurité Publique n° _______</div>
            <div>Commissariat de Sécurité Publique n° _______</div>
          </div>

          <!-- Center (Official Logo) -->
          <div style="text-align: center; padding: 0 20px;">
            <img src="/police-logo.svg" alt="Police Nationale du Tchad" style="
              width: 80px;
              height: 80px;
              margin: 0 auto;
              display: block;
            " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
            <div style="
              width: 80px;
              height: 80px;
              border: 2px solid #000;
              border-radius: 50%;
              display: none;
              align-items: center;
              justify-content: center;
              font-size: 10px;
              font-weight: bold;
              background: #f9f9f9;
              margin: 0 auto;
            ">
              LOGO<br>POLICE<br>NATIONALE
            </div>
          </div>

          <!-- Right Side (Arabic) -->
          <div style="text-align: right; font-size: 11px; line-height: 1.3; direction: rtl;">
            <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">جمهورية تشاد</div>
            <div style="margin-bottom: 3px;">رئاسة الجمهورية</div>
            <div style="margin-bottom: 3px;">وزارة الأمن العام والهجرة</div>
            <div style="margin-bottom: 3px;">الإدارة العامة للشرطة الوطنية</div>
            <div style="margin-bottom: 3px;">إدارة الأمن العام</div>
            <div style="margin-bottom: 3px;">المفوضية المركزية للأمن العام رقم _______</div>
            <div>مفوضية الأمن العام رقم _______</div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Text-based PDF generation with pagination (fallback)
   */
  private async generateTextBasedPDF(complaint: Complaint): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    const maxY = pageHeight - margin - 20 // Reserve space for footer
    let yPosition = margin

    // Helper function to check if we need a new page
    const checkNewPage = (requiredSpace: number = 10): void => {
      if (yPosition + requiredSpace > maxY) {
        pdf.addPage()
        yPosition = margin + 20
        // Add small header on new pages
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'bold')
        pdf.text('RÉPUBLIQUE DU TCHAD - POLICE NATIONALE', pageWidth / 2, margin + 10, { align: 'center' })
        yPosition += 15
      }
    }

    // Page 1: Header and basic info
    this.addTextBasedHeader(pdf, pageWidth, margin)
    yPosition += 50

    // Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RAPPORT DE PLAINTE', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Basic info
    pdf.setFontSize(11)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Numéro: ${complaint.complaint_number}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date: ${this.formatDate(complaint.date_registered)}`, margin, yPosition)
    yPosition += 15

    // General Information
    checkNewPage(30)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS GÉNÉRALES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Type: ${complaint.type_name_fr || 'Non spécifié'}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Lieu: ${complaint.location_incident}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date incident: ${this.formatDate(complaint.date_incident)}`, margin, yPosition)
    yPosition += 15

    // Complainant Information
    checkNewPage(40)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS DU PLAIGNANT', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Nom: ${complaint.complainant_name}`, margin, yPosition)
    yPosition += 6
    if (complaint.complainant_id_number) {
      pdf.text(`ID: ${complaint.complainant_id_number}`, margin, yPosition)
      yPosition += 6
    }
    if (complaint.complainant_phone) {
      pdf.text(`Téléphone: ${complaint.complainant_phone}`, margin, yPosition)
      yPosition += 6
    }
    if (complaint.complainant_address) {
      pdf.text(`Adresse: ${complaint.complainant_address}`, margin, yPosition)
      yPosition += 6
    }
    yPosition += 10

    // Accused Information (Always show section)
    checkNewPage(25)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS SUR LE MIS EN CAUSE', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    if ((complaint as any).accused_name) {
      pdf.text(`Nom: ${(complaint as any).accused_name}`, margin, yPosition)
      yPosition += 6
      pdf.text('Informations supplémentaires: [À compléter lors de l\'enquête]', margin, yPosition)
    } else {
      pdf.text('Nom: [Non spécifié / À déterminer]', margin, yPosition)
      yPosition += 6
      pdf.setFont('helvetica', 'italic')
      pdf.text('Les informations seront ajoutées lors de l\'avancement de l\'enquête.', margin, yPosition)
      pdf.setFont('helvetica', 'normal')
    }
    yPosition += 15

    // Description
    if (complaint.description_fr || complaint.description_ar) {
      checkNewPage(30)
      pdf.setFont('helvetica', 'bold')
      pdf.text('DESCRIPTION DE L\'INCIDENT', margin, yPosition)
      yPosition += 10

      pdf.setFont('helvetica', 'normal')

      if (complaint.description_fr) {
        pdf.text('Description (Français):', margin, yPosition)
        yPosition += 6
        const frenchLines = pdf.splitTextToSize(complaint.description_fr, pageWidth - 2 * margin)
        frenchLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }

      if (complaint.description_ar) {
        checkNewPage(15)
        pdf.text('Description (العربية):', margin, yPosition)
        yPosition += 6
        const arabicLines = pdf.splitTextToSize(complaint.description_ar, pageWidth - 2 * margin)
        arabicLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }
    }

    // Additional Notes
    if (complaint.evidence_notes || complaint.officer_notes) {
      checkNewPage(20)
      pdf.setFont('helvetica', 'bold')
      pdf.text('NOTES SUPPLÉMENTAIRES', margin, yPosition)
      yPosition += 10

      pdf.setFont('helvetica', 'normal')

      if (complaint.evidence_notes) {
        pdf.text('Notes sur les preuves:', margin, yPosition)
        yPosition += 6
        const evidenceLines = pdf.splitTextToSize(complaint.evidence_notes, pageWidth - 2 * margin)
        evidenceLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }

      if (complaint.officer_notes) {
        checkNewPage(15)
        pdf.text('Notes de l\'officier:', margin, yPosition)
        yPosition += 6
        const officerLines = pdf.splitTextToSize(complaint.officer_notes, pageWidth - 2 * margin)
        officerLines.forEach((line: string) => {
          checkNewPage(6)
          pdf.text(line, margin, yPosition)
          yPosition += 5
        })
        yPosition += 8
      }
    }

    // Official Information
    checkNewPage(25)
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS OFFICIELLES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Officier responsable: ${complaint.officer_in_charge}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Date de création: ${this.formatDate(complaint.created_at || complaint.date_registered)}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Dernière modification: ${this.formatDate(complaint.updated_at || complaint.date_registered)}`, margin, yPosition)
    yPosition += 20

    // Signature and Stamp Section
    checkNewPage(60)
    pdf.setFont('helvetica', 'bold')
    pdf.text('VALIDATION OFFICIELLE', margin, yPosition)
    yPosition += 15

    // Signature area
    pdf.setFont('helvetica', 'normal')
    pdf.text('Le Commissaire (Signature):', margin, yPosition)
    yPosition += 10

    // Signature box
    pdf.setLineWidth(0.5)
    pdf.rect(margin, yPosition, 80, 30) // Signature box
    yPosition += 40

    // Official stamp area
    pdf.setFont('helvetica', 'bold')
    pdf.text('CACHET OFFICIEL:', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 10

    // Stamp circle
    const stampX = pageWidth / 2
    const stampY = yPosition + 20
    pdf.circle(stampX, stampY, 25, 'S') // Official stamp circle
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'normal')
    pdf.text('CACHET OFFICIEL', stampX, stampY - 5, { align: 'center' })
    pdf.text('POLICE NATIONALE', stampX, stampY, { align: 'center' })
    pdf.text('DU TCHAD', stampX, stampY + 5, { align: 'center' })

    // Add page numbers to all pages
    const totalPages = pdf.internal.getNumberOfPages()
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'normal')
      pdf.text(`Page ${i} sur ${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' })
    }

    // Save the PDF
    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Add text-based header for fallback PDF
   */
  private addTextBasedHeader(pdf: jsPDF, pageWidth: number, margin: number): void {
    pdf.setFontSize(12)
    pdf.setFont('helvetica', 'bold')
    pdf.text('RÉPUBLIQUE DU TCHAD', pageWidth / 2, margin + 5, { align: 'center' })
    pdf.setFontSize(10)
    pdf.text('Présidence de la République', pageWidth / 2, margin + 12, { align: 'center' })
    pdf.text('Ministère de la Sécurité Publique et de l\'Immigration', pageWidth / 2, margin + 18, { align: 'center' })
    pdf.text('Direction Générale de la Police Nationale', pageWidth / 2, margin + 24, { align: 'center' })
    pdf.text('Direction de la Sécurité Publique', pageWidth / 2, margin + 30, { align: 'center' })

    // Line under header
    pdf.setLineWidth(1)
    pdf.line(margin, margin + 40, pageWidth - margin, margin + 40)
  }

  /**
   * Create HTML content with proper Arabic font styling (legacy method)
   */
  private createBilingualHTML(complaint: Complaint): string {
    const statusFr = this.getStatusText(complaint.status).split(' / ')[0]
    const statusAr = this.getStatusText(complaint.status).split(' / ')[1] || 'غير محدد'
    const priorityFr = this.getPriorityText(complaint.priority).split(' / ')[0]
    const priorityAr = this.getPriorityText(complaint.priority).split(' / ')[1] || 'غير محدد'

    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        padding: 20px;
        width: 210mm;
        min-height: 297mm;
      ">
        <!-- Official Header -->
        <div style="margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 15px;">
          <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: center;">
            <!-- Left Side (French) -->
            <div style="text-align: left; font-size: 11px; line-height: 1.3;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">RÉPUBLIQUE DU TCHAD</div>
              <div style="margin-bottom: 3px;">Présidence de la République</div>
              <div style="margin-bottom: 3px;">Ministère de la Sécurité Publique et de l'Immigration</div>
              <div style="margin-bottom: 3px;">Direction Générale de la Police Nationale</div>
              <div style="margin-bottom: 3px;">Direction de la Sécurité Publique</div>
              <div style="margin-bottom: 3px;">Commissariat Central de Sécurité Publique n° _______</div>
              <div>Commissariat de Sécurité Publique n° _______</div>
            </div>

            <!-- Center (Logo Placeholder) -->
            <div style="text-align: center; padding: 0 20px;">
              <div style="
                width: 80px;
                height: 80px;
                border: 2px solid #000;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
                background: #f9f9f9;
                margin: 0 auto;
              ">
                LOGO<br>POLICE<br>NATIONALE
              </div>
            </div>

            <!-- Right Side (Arabic) -->
            <div style="text-align: right; font-size: 11px; line-height: 1.3; direction: rtl;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">جمهورية تشاد</div>
              <div style="margin-bottom: 3px;">رئاسة الجمهورية</div>
              <div style="margin-bottom: 3px;">وزارة الأمن العام والهجرة</div>
              <div style="margin-bottom: 3px;">الإدارة العامة للشرطة الوطنية</div>
              <div style="margin-bottom: 3px;">إدارة الأمن العام</div>
              <div style="margin-bottom: 3px;">المفوضية المركزية للأمن العام رقم _______</div>
              <div>مفوضية الأمن العام رقم _______</div>
            </div>
          </div>
        </div>

        <!-- Title -->
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0;">RAPPORT DE PLAINTE</h2>
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0; direction: rtl;">تقرير الشكوى</h2>
        </div>

        <!-- Complaint Info -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <div>
            <strong>Numéro:</strong> ${complaint.complaint_number}<br>
            <strong>Date:</strong> ${this.formatDate(complaint.date_registered)}
          </div>
          <div style="text-align: right; direction: rtl;">
            <strong>رقم:</strong> ${complaint.complaint_number}<br>
            <strong>التاريخ:</strong> ${this.formatDate(complaint.date_registered)}
          </div>
        </div>

        <!-- General Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS GÉNÉRALES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات عامة</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Type de Plainte:</strong><br>
                ${complaint.type_name_fr || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Lieu de l'Incident:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de l'Incident:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>نوع الشكوى:</strong><br>
                ${complaint.type_name_ar || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>مكان الحادث:</strong><br>
                ${complaint.location_incident}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الحادث:</strong><br>
                ${this.formatDate(complaint.date_incident)}
              </div>
            </div>
          </div>
        </div>

        <!-- Complainant Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS DU PLAIGNANT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكي</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom Complet:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Numéro d'Identité:</strong><br>
                ${complaint.complainant_id_number || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Téléphone:</strong><br>
                ${complaint.complainant_phone || 'Non spécifié'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Adresse:</strong><br>
                ${complaint.complainant_address || 'Non spécifiée'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الاسم الكامل:</strong><br>
                ${complaint.complainant_name}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>رقم الهوية:</strong><br>
                ${complaint.complainant_id_number || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>الهاتف:</strong><br>
                ${complaint.complainant_phone || 'غير محدد'}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>العنوان:</strong><br>
                ${complaint.complainant_address || 'غير محدد'}
              </div>
            </div>
          </div>
        </div>

        ${(complaint as any).accused_name ? `
        <!-- Accused Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS SUR LE MIS EN CAUSE</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات المشتكى عليه</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Nom du Mis en Cause:</strong><br>
                ${(complaint as any).accused_name}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>اسم المشتكى عليه:</strong><br>
                ${(complaint as any).accused_name}
              </div>
            </div>
          </div>
        </div>
        ` : ''}

        <!-- Description -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">DESCRIPTION DE L'INCIDENT</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">وصف الحادث</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <strong>Description en Français:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
                ${complaint.description_fr || '[Description non fournie en français]'}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <strong>الوصف بالعربية:</strong><br>
              <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd; text-align: right; direction: rtl;">
                ${complaint.description_ar || '[لم يتم تقديم وصف بالعربية]'}
              </div>
            </div>
          </div>
        </div>

        ${complaint.evidence_notes || complaint.officer_notes ? `
        <!-- Additional Notes -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">NOTES SUPPLÉMENTAIRES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">ملاحظات إضافية</h3>
          </div>

          ${complaint.evidence_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes sur les Preuves:</strong>
              <strong style="direction: rtl;">ملاحظات الأدلة:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.evidence_notes}
            </div>
          </div>
          ` : ''}

          ${complaint.officer_notes ? `
          <div style="margin-bottom: 15px;">
            <div style="display: flex; justify-content: space-between;">
              <strong>Notes de l'Officier:</strong>
              <strong style="direction: rtl;">ملاحظات الضابط:</strong>
            </div>
            <div style="background: #f9f9f9; padding: 10px; margin-top: 5px; border: 1px solid #ddd;">
              ${complaint.officer_notes}
            </div>
          </div>
          ` : ''}
        </div>
        ` : ''}

        <!-- Official Information -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">INFORMATIONS OFFICIELLES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">معلومات رسمية</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;">
                <strong>Officier Responsable:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Date de Création:</strong><br>
                ${this.formatDate(complaint.created_at || complaint.date_registered)}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>Dernière Modification:</strong><br>
                ${this.formatDate(complaint.updated_at || complaint.date_registered)}
              </div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;">
                <strong>الضابط المسؤول:</strong><br>
                ${complaint.officer_in_charge}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>تاريخ الإنشاء:</strong><br>
                ${this.formatDate(complaint.created_at || complaint.date_registered)}
              </div>
              <div style="margin-bottom: 8px;">
                <strong>آخر تعديل:</strong><br>
                ${this.formatDate(complaint.updated_at || complaint.date_registered)}
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Fallback PDF generation using text-only approach
   */
  private async generateFallbackPDF(complaint: Complaint): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    let yPosition = margin

    // Simple text-based PDF as fallback
    pdf.setFont('helvetica', 'bold')
    pdf.setFontSize(16)
    pdf.text('RAPPORT DE PLAINTE', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    pdf.setFont('helvetica', 'normal')
    pdf.setFontSize(12)
    pdf.text(`Numéro: ${complaint.complaint_number}`, margin, yPosition)
    yPosition += 10
    pdf.text(`Date: ${this.formatDate(complaint.date_registered)}`, margin, yPosition)
    yPosition += 15

    // Add basic information
    pdf.setFont('helvetica', 'bold')
    pdf.text('INFORMATIONS GÉNÉRALES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Type: ${complaint.type_name_fr || 'Non spécifié'}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Plaignant: ${complaint.complainant_name}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Lieu: ${complaint.location_incident}`, margin, yPosition)
    yPosition += 6
    pdf.text(`Officier: ${complaint.officer_in_charge}`, margin, yPosition)
    yPosition += 15

    if (complaint.description_fr) {
      pdf.setFont('helvetica', 'bold')
      pdf.text('DESCRIPTION:', margin, yPosition)
      yPosition += 8
      pdf.setFont('helvetica', 'normal')
      const lines = pdf.splitTextToSize(complaint.description_fr, pageWidth - 2 * margin)
      lines.forEach((line: string) => {
        pdf.text(line, margin, yPosition)
        yPosition += 5
      })
    }

    pdf.save(`Plainte_${complaint.complaint_number}.pdf`)
  }

  /**
   * Generate a summary report of multiple complaints with proper pagination
   */
  public async generateSummaryReport(complaints: Complaint[], title: string = 'RAPPORT RÉCAPITULATIF'): Promise<void> {
    try {
      // Use text-based approach for summary reports (more reliable for large datasets)
      await this.generateTextBasedSummaryPDF(complaints, title)
    } catch (error) {
      console.error('Error generating summary PDF:', error)
      // Fallback to simple summary
      await this.generateFallbackSummaryPDF(complaints, title)
    }
  }

  /**
   * Generate text-based summary PDF with proper pagination
   */
  private async generateTextBasedSummaryPDF(complaints: Complaint[], title: string): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const pageHeight = pdf.internal.pageSize.getHeight()
    const margin = 15
    const maxY = pageHeight - margin - 20
    let yPosition = margin

    // Helper function for new page
    const checkNewPage = (requiredSpace: number = 10): void => {
      if (yPosition + requiredSpace > maxY) {
        pdf.addPage()
        yPosition = margin + 20
        // Add header on new pages
        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'bold')
        pdf.text('RÉPUBLIQUE DU TCHAD - POLICE NATIONALE', pageWidth / 2, margin + 10, { align: 'center' })
        pdf.text(title, pageWidth / 2, margin + 16, { align: 'center' })
        yPosition += 25
      }
    }

    // Header
    this.addTextBasedHeader(pdf, pageWidth, margin)
    yPosition += 50

    // Title
    pdf.setFontSize(16)
    pdf.setFont('helvetica', 'bold')
    pdf.text(title, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    // Report info
    const reportDate = this.formatDate(new Date().toISOString())
    pdf.setFontSize(10)
    pdf.setFont('helvetica', 'normal')
    pdf.text(`Date du rapport: ${reportDate}`, margin, yPosition)
    pdf.text(`Nombre de plaintes: ${complaints.length}`, pageWidth - margin, yPosition, { align: 'right' })
    yPosition += 15

    // Statistics
    const stats = this.calculateStatistics(complaints)
    checkNewPage(40)

    pdf.setFont('helvetica', 'bold')
    pdf.text('STATISTIQUES', margin, yPosition)
    yPosition += 10

    pdf.setFont('helvetica', 'normal')
    pdf.text(`Total: ${stats.total}`, margin, yPosition)
    pdf.text(`En attente: ${stats.pending}`, margin + 40, yPosition)
    pdf.text(`En investigation: ${stats.investigating}`, margin + 80, yPosition)
    yPosition += 6
    pdf.text(`Résolues: ${stats.resolved}`, margin, yPosition)
    pdf.text(`Fermées: ${stats.closed}`, margin + 40, yPosition)
    yPosition += 15

    // Complaints list
    checkNewPage(30)
    pdf.setFont('helvetica', 'bold')
    pdf.text('LISTE DES PLAINTES', margin, yPosition)
    yPosition += 10

    // Table header
    pdf.setFontSize(8)
    pdf.setFont('helvetica', 'bold')
    pdf.text('Numéro', margin, yPosition)
    pdf.text('Plaignant', margin + 30, yPosition)
    pdf.text('Type', margin + 70, yPosition)
    pdf.text('Statut', margin + 110, yPosition)
    pdf.text('Date', margin + 140, yPosition)
    pdf.text('Officier', margin + 170, yPosition)
    yPosition += 6

    // Line under header
    pdf.setLineWidth(0.3)
    pdf.line(margin, yPosition, pageWidth - margin, yPosition)
    yPosition += 4

    // Complaints data
    pdf.setFont('helvetica', 'normal')
    complaints.forEach((complaint, index) => {
      checkNewPage(8)

      const statusText = this.getStatusText(complaint.status).split(' / ')[0]
      const typeText = (complaint.type_name_fr || '').substring(0, 15)
      const complainantName = complaint.complainant_name.substring(0, 15)
      const officerName = complaint.officer_in_charge.substring(0, 12)
      const dateText = this.formatDate(complaint.date_registered).split(' ')[0]

      pdf.text(complaint.complaint_number, margin, yPosition)
      pdf.text(complainantName, margin + 30, yPosition)
      pdf.text(typeText, margin + 70, yPosition)
      pdf.text(statusText, margin + 110, yPosition)
      pdf.text(dateText, margin + 140, yPosition)
      pdf.text(officerName, margin + 170, yPosition)
      yPosition += 5

      // Add separator line every 5 rows
      if ((index + 1) % 5 === 0) {
        pdf.setLineWidth(0.1)
        pdf.line(margin, yPosition, pageWidth - margin, yPosition)
        yPosition += 2
      }
    })

    // Add page numbers
    const totalPages = pdf.internal.getNumberOfPages()
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i)
      pdf.setFontSize(8)
      pdf.setFont('helvetica', 'normal')
      pdf.text(`Page ${i} sur ${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' })
    }

    // Save the PDF
    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  /**
   * Create HTML content for summary report
   */
  private createSummaryHTML(complaints: Complaint[], title: string): string {
    const stats = this.calculateStatistics(complaints)
    const reportDate = this.formatDate(new Date().toISOString())

    return `
      <div style="
        font-family: Arial, 'Noto Naskh Arabic', 'Amiri', 'Arabic Typesetting', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: #fff;
        padding: 20px;
        width: 210mm;
        min-height: 297mm;
      ">
        <!-- Official Header -->
        <div style="margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 15px;">
          <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 20px; align-items: center;">
            <!-- Left Side (French) -->
            <div style="text-align: left; font-size: 11px; line-height: 1.3;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">RÉPUBLIQUE DU TCHAD</div>
              <div style="margin-bottom: 3px;">Présidence de la République</div>
              <div style="margin-bottom: 3px;">Ministère de la Sécurité Publique et de l'Immigration</div>
              <div style="margin-bottom: 3px;">Direction Générale de la Police Nationale</div>
              <div style="margin-bottom: 3px;">Direction de la Sécurité Publique</div>
              <div style="margin-bottom: 3px;">Commissariat Central de Sécurité Publique n° _______</div>
              <div>Commissariat de Sécurité Publique n° _______</div>
            </div>

            <!-- Center (Logo Placeholder) -->
            <div style="text-align: center; padding: 0 20px;">
              <div style="
                width: 80px;
                height: 80px;
                border: 2px solid #000;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
                background: #f9f9f9;
                margin: 0 auto;
              ">
                LOGO<br>POLICE<br>NATIONALE
              </div>
            </div>

            <!-- Right Side (Arabic) -->
            <div style="text-align: right; font-size: 11px; line-height: 1.3; direction: rtl;">
              <div style="font-weight: bold; font-size: 14px; margin-bottom: 8px;">جمهورية تشاد</div>
              <div style="margin-bottom: 3px;">رئاسة الجمهورية</div>
              <div style="margin-bottom: 3px;">وزارة الأمن العام والهجرة</div>
              <div style="margin-bottom: 3px;">الإدارة العامة للشرطة الوطنية</div>
              <div style="margin-bottom: 3px;">إدارة الأمن العام</div>
              <div style="margin-bottom: 3px;">المفوضية المركزية للأمن العام رقم _______</div>
              <div>مفوضية الأمن العام رقم _______</div>
            </div>
          </div>
        </div>

        <!-- Title -->
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0;">${title}</h2>
          <h2 style="font-size: 18px; font-weight: bold; margin: 5px 0; direction: rtl;">تقرير ملخص</h2>
        </div>

        <!-- Report Info -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <div>
            <strong>Date du Rapport:</strong> ${reportDate}<br>
            <strong>Nombre de Plaintes:</strong> ${complaints.length}
          </div>
          <div style="text-align: right; direction: rtl;">
            <strong>تاريخ التقرير:</strong> ${reportDate}<br>
            <strong>عدد الشكاوى:</strong> ${complaints.length}
          </div>
        </div>

        <!-- Statistics -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">STATISTIQUES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">إحصائيات</h3>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <div style="margin-bottom: 8px;"><strong>Total des Plaintes:</strong> ${stats.total}</div>
              <div style="margin-bottom: 8px;"><strong>En Attente:</strong> ${stats.pending}</div>
              <div style="margin-bottom: 8px;"><strong>En Investigation:</strong> ${stats.investigating}</div>
              <div style="margin-bottom: 8px;"><strong>Résolues:</strong> ${stats.resolved}</div>
              <div style="margin-bottom: 8px;"><strong>Fermées:</strong> ${stats.closed}</div>
            </div>

            <div style="text-align: right; direction: rtl;">
              <div style="margin-bottom: 8px;"><strong>إجمالي الشكاوى:</strong> ${stats.total}</div>
              <div style="margin-bottom: 8px;"><strong>في الانتظار:</strong> ${stats.pending}</div>
              <div style="margin-bottom: 8px;"><strong>قيد التحقيق:</strong> ${stats.investigating}</div>
              <div style="margin-bottom: 8px;"><strong>محلولة:</strong> ${stats.resolved}</div>
              <div style="margin-bottom: 8px;"><strong>مغلقة:</strong> ${stats.closed}</div>
            </div>
          </div>
        </div>

        <!-- Complaints Table -->
        <div style="margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px; border-bottom: 1px solid #000; padding-bottom: 5px;">
            <h3 style="font-size: 14px; font-weight: bold;">LISTE DES PLAINTES</h3>
            <h3 style="font-size: 14px; font-weight: bold; direction: rtl;">قائمة الشكاوى</h3>
          </div>

          <table style="width: 100%; border-collapse: collapse; font-size: 10px;">
            <thead>
              <tr style="background: #f0f0f0;">
                <th style="border: 1px solid #ccc; padding: 5px; text-align: left;">Numéro</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: left;">Plaignant</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: left;">Type</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: left;">Statut</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: left;">Date</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: right; direction: rtl;">رقم</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: right; direction: rtl;">المشتكي</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: right; direction: rtl;">النوع</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: right; direction: rtl;">الحالة</th>
                <th style="border: 1px solid #ccc; padding: 5px; text-align: right; direction: rtl;">التاريخ</th>
              </tr>
            </thead>
            <tbody>
              ${complaints.slice(0, 20).map(complaint => `
                <tr>
                  <td style="border: 1px solid #ccc; padding: 3px;">${complaint.complaint_number}</td>
                  <td style="border: 1px solid #ccc; padding: 3px;">${complaint.complainant_name.substring(0, 15)}${complaint.complainant_name.length > 15 ? '...' : ''}</td>
                  <td style="border: 1px solid #ccc; padding: 3px;">${(complaint.type_name_fr || '').substring(0, 12)}</td>
                  <td style="border: 1px solid #ccc; padding: 3px;">${this.getStatusText(complaint.status).split(' / ')[0]}</td>
                  <td style="border: 1px solid #ccc; padding: 3px;">${this.formatDate(complaint.date_registered).split(' ')[0]}</td>
                  <td style="border: 1px solid #ccc; padding: 3px; text-align: right; direction: rtl;">${complaint.complaint_number}</td>
                  <td style="border: 1px solid #ccc; padding: 3px; text-align: right; direction: rtl;">${complaint.complainant_name.substring(0, 15)}${complaint.complainant_name.length > 15 ? '...' : ''}</td>
                  <td style="border: 1px solid #ccc; padding: 3px; text-align: right; direction: rtl;">${(complaint.type_name_ar || complaint.type_name_fr || '').substring(0, 12)}</td>
                  <td style="border: 1px solid #ccc; padding: 3px; text-align: right; direction: rtl;">${this.getStatusText(complaint.status).split(' / ')[1] || this.getStatusText(complaint.status).split(' / ')[0]}</td>
                  <td style="border: 1px solid #ccc; padding: 3px; text-align: right; direction: rtl;">${this.formatDate(complaint.date_registered).split(' ')[0]}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          ${complaints.length > 20 ? `
          <div style="margin-top: 10px; font-style: italic; text-align: center;">
            Affichage des 20 premières plaintes sur ${complaints.length} au total
          </div>
          ` : ''}
        </div>

        <!-- Footer -->
        <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; text-align: center; font-size: 10px; color: #666; border-top: 1px solid #ccc; padding-top: 10px;">
          <div>Document généré automatiquement par le Système de Gestion des Plaintes</div>
          <div style="direction: rtl; margin-top: 3px;">وثيقة تم إنشاؤها تلقائياً بواسطة نظام إدارة الشكاوى</div>
        </div>
      </div>
    `
  }

  /**
   * Fallback summary PDF generation
   */
  private async generateFallbackSummaryPDF(complaints: Complaint[], title: string): Promise<void> {
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = pdf.internal.pageSize.getWidth()
    const margin = 15
    let yPosition = margin

    pdf.setFont('helvetica', 'bold')
    pdf.setFontSize(16)
    pdf.text(title, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    const stats = this.calculateStatistics(complaints)
    pdf.setFont('helvetica', 'normal')
    pdf.setFontSize(12)
    pdf.text(`Total: ${stats.total}, En Attente: ${stats.pending}, Résolues: ${stats.resolved}`, margin, yPosition)
    yPosition += 15

    // Simple list of complaints
    complaints.slice(0, 30).forEach(complaint => {
      if (yPosition > 250) {
        pdf.addPage()
        yPosition = margin
      }
      pdf.text(`${complaint.complaint_number} - ${complaint.complainant_name}`, margin, yPosition)
      yPosition += 6
    })

    pdf.save(`Rapport_Recapitulatif_${new Date().toISOString().split('T')[0]}.pdf`)
  }

  // Old text-based PDF methods removed - now using HTML-to-canvas approach for proper Arabic rendering

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  private getStatusText(status: string): string {
    const statusMap = {
      pending: 'En Attente / في الانتظار',
      investigating: 'En Investigation / قيد التحقيق',
      resolved: 'Résolue / محلولة',
      closed: 'Fermée / مغلقة'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  private getPriorityText(priority: string): string {
    const priorityMap = {
      low: 'Faible / منخفض',
      medium: 'Moyen / متوسط',
      high: 'Élevé / عالي',
      urgent: 'Urgent / عاجل'
    }
    return priorityMap[priority as keyof typeof priorityMap] || priority
  }

  private calculateStatistics(complaints: Complaint[]) {
    return {
      total: complaints.length,
      pending: complaints.filter(c => c.status === 'pending').length,
      investigating: complaints.filter(c => c.status === 'investigating').length,
      resolved: complaints.filter(c => c.status === 'resolved').length,
      closed: complaints.filter(c => c.status === 'closed').length
    }
  }
}

export const pdfGenerator = PDFGenerator.getInstance()
